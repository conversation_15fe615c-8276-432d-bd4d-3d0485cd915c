package com.fxiaoke.stone.commons.domain.api.adapt;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * RestEasyR
 * 用于适配RestEasy的响应结果
 * 目前没有任何字段,仅作为标识
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RestEasyR<T> {

  /**
   * 成功返回 0 其他均为错误码
   */
  private int code;

  /**
   * 成功返回 OK 其他均为错误信息
   */
  private String message;

  /**
   * 响应数据
   * 成功时返回具体数据,失败时返回null
   */
  private T data;
}
