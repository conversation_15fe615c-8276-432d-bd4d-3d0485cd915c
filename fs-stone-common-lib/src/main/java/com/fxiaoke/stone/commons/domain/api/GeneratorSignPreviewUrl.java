package com.fxiaoke.stone.commons.domain.api;

import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.AcUser;
import com.fxiaoke.stone.commons.domain.model.FileInfo;
import com.fxiaoke.stone.commons.domain.model.FileUser;
import com.fxiaoke.stone.commons.domain.model.GeneratorSignUrlBaseArg;
import com.fxiaoke.stone.commons.domain.model.GeneratorSignUrlBaseResult;
import com.fxiaoke.stone.commons.domain.utils.FileInfoUtil;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface GeneratorSignPreviewUrl {

  /**
   * 生成签名预览普通文件链接参数类
   * <p>
   * 此类用于生成免身份预览普通文件链接的参数信息，包含访问用户、文件用户及文件信息的详细属性和规则。
   * </p>
   *
   * <h2>访问用户类 {@link AcUser} 谁使用该链接预览</h2>
   * <ul>
   *   <li><strong>tenantId:</strong> 租户ID</li>
   *   <li><strong>userId:</strong> 用户ID</li>
   *   <li><strong>upstreamOwnerId:</strong> 上游负责人用户ID，默认为-9527表示缺少上游负责人</li>
   *   <li><strong>outTenantId:</strong> 外部租户ID</li>
   *   <li><strong>outUserId:</strong> 外部用户ID</li>
   * </ul>
   * <p>允许的组合:</p>
   * <ol>
   *   <li>tenantId + userId</li>
   *   <li>tenantId + outTenantId + outUserId + upstreamOwnerId</li>
   * </ol>
   *
   * <h2>文件用户类 {@link FileUser} 该文件属于谁</h2>
   * <p>使用生成链接的访问用户使用文件用户权限预览文件</p>
   * <ul>
   *   <li><strong>tenantId:</strong> 租户ID</li>
   *   <li><strong>userId:</strong> 用户ID (可以为空，A类型文件需传具有该文件访问权限的员工ID)</li>
   *   <li><strong>securityGroup:</strong> 安全组 (可以为空，下载文件为网盘文件需传 XiaoKeNetDisk)</li>
   * </ul>
   *
   * <h2>文件信息类 {@link FileInfo} 文件的基本信息</h2>
   * <ul>
   *   <li><strong>path:</strong> 文件ID，长度不应超过48，目前仅支持N_、TN_、C_、TC_开头，不需要携带扩展名</li>
   *   <li><strong>filename:</strong> 文件名，长度不应超过128，超出部分会被截断。空值默认为YYYY-MM-DD-HH+扩展名</li>
   *   <li><strong>extension:</strong> 文件扩展名，不需要带点号</li>
   *   <ol>
   *     <li>支持浏览器可以直接打开的文件类型,目前仅支持以下类型,如需支持其他类型可反馈添加</li>
   *     <li>图片类型：png、webp、jpeg、jpg、bmp、gif</li>
   *     <li>纯文本：txt、sql、js、css、csv、json、md、xml、py、java</li>
   *     <li>多媒体：mp4、mp3</li>
   *   </ol>
   *   <li><strong>expireTime:</strong> 生成链接的有效期，单位秒/s (60-31536000)</li>
   *   <li><strong>business:</strong> 业务标识，长度不超过24个字符</li>
   *   <li><strong>globalAcceleration:</strong> 是否开启全球加速，默认为false</li>
   * </ul>
   *
   * <p>此类继承自 {@link GeneratorSignUrlBaseArg}，提供参数的基础构造和扩展。</p>
   */
  @Data
  @ToString(callSuper = true)
  @EqualsAndHashCode(callSuper = true)
  class Arg extends GeneratorSignUrlBaseArg {

    @Min(value = 60, message = "Expire time cannot be less than 60 seconds")
    @Max(value = 63072000, message = "Expire time cannot exceed 63072000 seconds")
    private long expireTime;

    protected void checkExpireTime() {
      if (expireTime < Constants.MIN_EXPIRE_TIME || expireTime > Constants.LONG_MAX_EXPIRE_TIME) {
        throw new StoneCommonClientException("Expire time must be between " + Constants.MIN_EXPIRE_TIME + " and " + Constants.MAX_EXPIRE_TIME + " seconds", 400);
      }
    }

    public Arg() {
      super();
    }

    public Arg(AcUser acUser, FileUser fileUser, FileInfo fileInfo, long expireTime, String business,
        boolean globalAcceleration) {
      super(acUser, fileUser, fileInfo, expireTime, business, globalAcceleration);
    }

    @Override
    public void check() {
      super.check();
      if (!FileInfoUtil.isSupportCommonPreview(getFileInfo().getExtension())){
        throw new StoneCommonClientException("Not support preview file type",400);
      }
    }
  }

  /**
   * 生成签名下载链接结果类。
   * <p>
   * 此类用于表示生成的下载链接结果，其中包含文件路径和对应的下载链接。
   * </p>
   *
   * <ul>
   *   <li><strong>path:</strong> 文件路径，与下载链接一一对应。</li>
   *   <li><strong>url:</strong> 下载链接，与文件路径一一对应。</li>
   * </ul>
   *
   * <p>此类继承自 {@link GeneratorSignUrlBaseResult}，提供结果的基础构造和扩展。</p>
   */
  @Data
  @ToString(callSuper = true)
  @EqualsAndHashCode(callSuper = true)
  class Result extends GeneratorSignUrlBaseResult {


    /**
     * 默认构造函数。
     */
    public Result() {
      super();
    }

    /**
     * 带参数的构造函数。
     *
     * @param path 文件路径
     * @param url 下载链接
     */
    public Result(String path, String url) {
      super(path, url);
    }

    /**
     * 静态工厂方法，用于创建 {@code Result} 实例。
     *
     * @param path 文件路径
     * @param url 下载链接
     * @return 新创建的 {@code Result} 实例
     */
    public static Result of(String path, String url) {
      return new Result(path, url);
    }

  }

}
