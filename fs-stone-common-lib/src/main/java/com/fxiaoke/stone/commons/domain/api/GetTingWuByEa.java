package com.fxiaoke.stone.commons.domain.api;

import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * creator: liuys
 * CreateTime: 2024-12-20
 * Description: 根据ea 获取阿里听悟配置
 */
public interface GetTingWuByEa {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg {
    @NotBlank(message = "ea can not be empty")
    String ea;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result {

    private String appKey; // 听悟appKey
    private String accessKey;
    private String secretKey;
    private String stsToken; // sts 认证模式下的临时安全令牌
    private String endPoint; // 听悟服务endPoint
    private String transResultOssBucket; // 识别及智能提取结果写入到的 OSS Bucket，需要与管控台项目配置的 OSS Bucket 一致。
  }
}
