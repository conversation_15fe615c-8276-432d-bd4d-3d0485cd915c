package com.fxiaoke.stone.commons.domain.model;

import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.utils.FilenameUtil;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import com.fxiaoke.stone.commons.domain.validator.DocumentPreviewExtGroup;
import com.fxiaoke.stone.commons.domain.validator.PreviewExtGroup;
import java.io.UnsupportedEncodingException;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 文件信息类 path: 文件id <br>
 * 1.长度不应超出48,仅支持N_、TN_、C_、TC_开头,不需要携带扩展名<br> filename: 文件名<br>
 * 1.长度不应超出128,超出部分会被截断<br>
 * 2.空值默认为YYYY-MM-DD-HH+扩展名<br>
 * 3.文件名中包含不支持URL编码的字符时,会使用默认文件名<br> extension: 文件扩展名<br>
 * 1.不需要带点号<br>
 * 2.空值默认为bin<br>
 * 3.仅支持字母、数字<br>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class FileInfo {

  @Pattern(regexp = "^(N_|TN_|C_|TC_).{0,48}$", message = "Invalid file path format")
  @NotBlank(message = "File path cannot be empty")
  private String path;

  @Size(max = 120, message = "Filename cannot exceed 120 characters")
  private String filename;

  @Pattern(groups = {DocumentPreviewExtGroup.class}, regexp = "^(pdf|doc|docx|xls|xlsx|ppt|pptx)$",
      message = "Invalid document type")
  @Pattern(groups = {PreviewExtGroup.class}, regexp = "^(png|webp|jpeg|jpg|bmp|gif|txt|sql|js|css|csv|json|md|xml|py|java|mp4|mp3)$",
      message = "Invalid file preview type")
  @Pattern(regexp = "[a-zA-Z0-9]+", message = "Extension can only contain letters and numbers")
  @Size(max = 10, message = "Extension cannot exceed 10 characters")
  private String extension;

  public void check() {
    if (Strings.isNullOrEmpty(path)) {
      throw new StoneCommonClientException("FileInfo.path cannot be null or empty", 400);
    }
    if (!path.matches("^(N_|TN_|C_|TC_).{0,48}$")) {
      throw new StoneCommonClientException(
          "FileInfo.path must start with N_, TN_, C_ or TC_ and length cannot exceed 48 characters",
          400);
    }
    if (Strings.isNotNullOrEmpty(filename) && filename.length() > 120) {
      throw new StoneCommonClientException("FileInfo.filename length cannot exceed 120 characters",
          400);
    }
    if (Strings.isNotNullOrEmpty(extension) && extension.length() > 10 && !extension.matches(
        "[a-zA-Z0-9]+")) {
      throw new StoneCommonClientException(
          "FileInfo.extension length cannot exceed 8 characters and can only contain letters and numbers",
          400);
    }
  }

  public Pair<String, String> getEncodedAndOriginalFilename() {
    // 确定最终的扩展名
    String finalExt = (extension == null || extension.isEmpty()) ? "bin" : extension;

    // 确定最终的文件名
    String finalFileName;
    if (filename == null || filename.isEmpty()) {
      finalFileName = FilenameUtil.generateFileName(finalExt);
    } else {
      // 检查文件名的扩展名是否与给定的扩展名一致
      String currentExt = FilenameUtil.getExtension(filename);
      if (!currentExt.equals(finalExt)) {
        finalFileName = FilenameUtil.replaceExtension(filename, finalExt);
      } else {
        finalFileName = filename;
      }
    }

    // 尝试对最终文件名进行URL编码
    try {
      String encodedFileName = FilenameUtil.encode(finalFileName);
      return Pair.of(finalFileName, encodedFileName);
    } catch (UnsupportedEncodingException e) {
      // 处理编码异常，使用默认文件名
      String defaultFilename = FilenameUtil.generateFileName(finalExt);
      return Pair.of(defaultFilename, defaultFilename);
    }
  }

}
