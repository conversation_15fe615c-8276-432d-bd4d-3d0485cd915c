package com.fxiaoke.stone.commons.domain.utils;

import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Objects;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;


@Slf4j
public class AES256Util {
  private AES256Util() {
  }
  private static final String aesKey = "nirtHUNF/Ct8J7sf40VaIQui0N5r8gcbxGXKxRhu1C4=";
  private static final String aesIv = "jwNz4Ia8OHVpPyEXIQjJ2g==";

  private static final byte[] keyBytes=Base64.decodeBase64(aesKey);
  private static final byte[] ivBytes=Base64.decodeBase64(aesIv);

  private static final Cipher decrypt_c;
  private static final Cipher encrypt_c;

  static {
    try {
      decrypt_c = Cipher.getInstance("AES/CBC/PKCS5Padding");
      encrypt_c = Cipher.getInstance("AES/CBC/PKCS5Padding");
      IvParameterSpec iv = new IvParameterSpec(ivBytes);
      SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
      decrypt_c.init(Cipher.DECRYPT_MODE, key, iv, new SecureRandom(keyBytes));
      encrypt_c.init(Cipher.ENCRYPT_MODE, key, iv, new SecureRandom(keyBytes));
    } catch (Exception e) {
      throw new StoneCommonClientException("AES256CipherUtil init error", 500);
    }
  }

  public static String encipher(String source) {
    try {
      byte[] resultByte = encrypt_c.doFinal(source.getBytes(StandardCharsets.UTF_8));
      return parseByte2HexStr(resultByte);
    } catch (Exception e) {
      throw new StoneCommonClientException("aes256 encode fail,input is:"+source, 400);
    }
  }

  //cipher存在线程安全问题
  public static synchronized String decrypt(String source) {
    String result;
    try {
      byte[] resultByte = decrypt_c.doFinal(Objects.requireNonNull(parseHexStr2Byte(source)));
      result = new String(resultByte, StandardCharsets.UTF_8);
    } catch (Exception e) {
      throw new StoneCommonClientException("aes256 decode fail,input is:"+source, 400);
    }
    return result;
  }

  public static String parseByte2HexStr(byte[] buf) {
    StringBuilder sb = new StringBuilder();
    for (byte b : buf) {
      String hex = Integer.toHexString(b & 0xFF);
      if (hex.length() == 1) {
        hex = '0' + hex;
      }
      sb.append(hex.toUpperCase());
    }

    return sb.toString();
  }

  public static byte[] parseHexStr2Byte(String hexStr) {
    if (hexStr.isEmpty()) {
      return new byte[0];
    }
    byte[] result = new byte[hexStr.length() / 2];
    for (int i = 0; i < hexStr.length() / 2; i++) {
      int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
      int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
      result[i] = (byte) (high * 16 + low);
    }
    return result;
  }

}
