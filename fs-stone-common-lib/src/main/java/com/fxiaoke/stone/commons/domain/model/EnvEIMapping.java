package com.fxiaoke.stone.commons.domain.model;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class EnvEIMapping implements Serializable {

  String protocol;
  String homeDomain;
  String cdnDomain;
  String domain;
  String contextPath;
  String cdnContextPath;
  String accessKey;
  String secretKey;

  public boolean isConfigItemEmpty() {
    return protocol == null||protocol.isEmpty()
        || homeDomain == null|| homeDomain.isEmpty()
        || cdnDomain == null|| cdnDomain.isEmpty()
        || domain == null|| domain.isEmpty()
        || contextPath == null|| contextPath.isEmpty()
        || cdnContextPath == null|| cdnContextPath.isEmpty()
        || accessKey == null|| accessKey.isEmpty()
        || secretKey == null|| secretKey.isEmpty();
  }
}
