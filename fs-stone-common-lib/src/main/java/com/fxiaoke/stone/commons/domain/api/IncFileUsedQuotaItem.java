package com.fxiaoke.stone.commons.domain.api;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IncFileUsedQuotaItem {

  @NotBlank(message = "ea cannot be blank")
  private String ea;

  private long quota; // 已使用配额，单位为字节

  private long count; // 文件数量
}
