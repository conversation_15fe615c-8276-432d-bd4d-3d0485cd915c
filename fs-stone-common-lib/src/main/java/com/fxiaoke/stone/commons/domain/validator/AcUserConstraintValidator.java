package com.fxiaoke.stone.commons.domain.validator;


import com.fxiaoke.stone.commons.domain.annotation.ValidatorAcUser;
import com.fxiaoke.stone.commons.domain.model.AcUser;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class AcUserConstraintValidator implements ConstraintValidator<ValidatorAcUser, AcUser> {

  @Override
  public boolean isValid(AcUser acUser, ConstraintValidatorContext context) {
    if (acUser == null) {
      return false;
    }
    if (acUser.getTenantId()<=0) {
      return false;
    }
    // 如果是外部用户，需要传入外部租户ID和外部用户ID
    if (acUser.getOutTenantId()>0 && acUser.getOutUserId()>0) {
      return true;
    }
    // 否则，需要传入内部用户ID
    return acUser.getUserId()>0;
  }
}
