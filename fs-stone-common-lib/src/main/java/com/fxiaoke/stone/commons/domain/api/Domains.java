package com.fxiaoke.stone.commons.domain.api;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface Domains {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    /**
     * 是谁要访问该文件
     */
    private long tenantId;
    /**
     * 域名灰度使用
     */
    private long userId;

    /**
     * 业务线标识（有特殊要求的业务线需要传入）
     */
    private String business;
  }

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {

    /**
     * 主域名访问列表
     */
    private List<String> homeDomains;

    /**
     * 文件访问域名列表
     * 用户的企业文件访问域名
     * 一般为N|TN|A|TA|G|S|F文件，C|TC类型同样可使用此域名访问
     */
    private List<String> fileDomains;

    /**
     * 静态文件 CDN 域名列表
     * 一般为开发上传的 图片、JS、CSS等静态资源文件
     */
    private List<String> staticCdnDomains;
    /**
     * 动态文件 CDN 域名列表
     * 如头像和用户上传的C|TC类型文件
     */
    private List<String> dynamicCdnDomains;
  }

}
