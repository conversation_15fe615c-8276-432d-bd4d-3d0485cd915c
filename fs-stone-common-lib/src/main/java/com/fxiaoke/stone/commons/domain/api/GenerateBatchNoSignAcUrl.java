package com.fxiaoke.stone.commons.domain.api;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GenerateBatchNoSignAcUrl {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    @Valid
    @Size(min = 1, max = 100)
    List<GenerateNoSignAcUrl.Arg> noSignFilesInfo;
  }

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {

    List<GenerateNoSignAcUrl.Result> acUrls;
  }
}
