package com.fxiaoke.stone.commons.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TingWuOmit {
  private String appKey;
  private String accessKey;
  private String secretKey;
  private String stsToken;
  private String endPoint;
  private String transResultOssBucket; // 识别及智能提取结果写入到的 OSS Bucket，需要与管控台项目配置的 OSS Bucket 一致。
}
