package com.fxiaoke.stone.commons.domain.api;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GenerateFileMetaSignature {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg {
    @NotNull
    @Min(1)
    Long tenantId;
    @Pattern(
        regexp = "^(N_|TN_|C_|TC_|TA_|A_).{0,48}$",
        message = "Invalid path"
    )
    String path;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result{
    String path;
    String signature;
  }
}
