package com.fxiaoke.stone.commons.domain.utils;

import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.time.Instant;

public class DataTimeUtil {
  private DataTimeUtil() {}
  public static long getExpiresTimestamp(long expires) {
    if (expires < 60) {
      throw new StoneCommonClientException(
          "expireTime is too short,Please set the expireTime more than 60", 400);
    }
    if (expires > 604800) {
      throw new StoneCommonClientException(
          "expireTime is too long,Please set the expireTime less than 604800", 400);
    }
    Instant currentInstant = Instant.now();
    Instant futureInstant = currentInstant.plusSeconds(expires);
    return futureInstant.toEpochMilli();
  }
}
