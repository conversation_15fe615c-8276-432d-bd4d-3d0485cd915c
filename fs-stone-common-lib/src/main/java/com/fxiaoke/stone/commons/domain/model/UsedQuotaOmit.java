package com.fxiaoke.stone.commons.domain.model;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UsedQuotaOmit {

  /**
   * 企业账号
   */
  private String ea;
  /**
   * 已使用文件存储配额，单位为字节
   */
  private Long usedQuota;
  /**
   * 配额最后更新时间（配额更新有延迟）
   */
  private Date lastUpdate;

}
