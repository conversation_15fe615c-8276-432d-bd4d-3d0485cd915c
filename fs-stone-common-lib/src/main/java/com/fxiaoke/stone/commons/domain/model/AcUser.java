package com.fxiaoke.stone.commons.domain.model;

import com.fxiaoke.stone.commons.domain.annotation.ValidatorAcUser;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 访问用户类（即 谁使用该链接下载文件）<br>
 * tenantId:租户ID <br>
 * userId:用户ID <br>
 * upstreamOwnerId:上游负责人用户ID <br>
 * outTenantId:外部租户ID <br>
 * outUserId:外部用户ID <br>
 * 允许的组合: <br>
 * 1. tenantId + userId<br>
 * 2. tenantId + outTenantId + outUserId + upstreamOwnerId<br>
 * 注：upstreamOwnerId为空时,默认值为-9527,表示缺少上游负责人
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ValidatorAcUser
public class AcUser {

  private long tenantId;
  private long userId;
  private long outTenantId;
  private long outUserId;
  private long upstreamOwnerId;

  public String getTenantIdStr() {
    return String.valueOf(tenantId);
  }

  public String getAcid() {
    if (isExternalAccess()) {
      if (upstreamOwnerId <= 0) {
        upstreamOwnerId = -9527;
      }
      return Joiner.on(".").join( tenantId, upstreamOwnerId, outTenantId, outUserId);
    }
    return Joiner.on(".").join( tenantId, userId);
  }

  private boolean isExternalAccess() {
    return outTenantId > 0 && outUserId > 0;
  }

  // 检查userid 是否合法有效
  private boolean isUserIdValid() {
    return userId == -10000 || userId > 0;
  }

  public void check() {
    if (tenantId <= 0) {
      throw new StoneCommonClientException("AcUser.tenantId must be greater than 0", 400);
    }
    if (!isExternalAccess() && !isUserIdValid()) {
      throw new StoneCommonClientException(
          "non-downstream enterprise access, The userId must be greater than 0 or be system preset userId",
          400);
    }
  }
}
