package com.fxiaoke.stone.commons.domain.validator;


import com.fxiaoke.stone.commons.domain.annotation.ValidatorUserChain;
import com.fxiaoke.stone.commons.domain.api.UserChainArg;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class UserChainConstraintValidator implements ConstraintValidator<ValidatorUserChain, UserChainArg> {

  @Override
  public boolean isValid(UserChainArg arg, ConstraintValidatorContext context) {
    if (arg == null) {
      return false;
    }
    if (Strings.isNullOrEmpty(arg.getTenantId()) || Long.parseLong(arg.getTenantId()) <= 0) {
      return false;
    }
    // 如果是外部用户，需要传入外部租户ID和外部用户ID
    if (Strings.isNotNullOrEmpty(arg.getOutTenantId()) && Strings.isNotNullOrEmpty(arg.getOutUserId())) {
      return true;
    }
    // 否则，需要传入内部用户ID
    return Strings.isNotNullOrEmpty(arg.getUserId());
  }
}
