package com.fxiaoke.stone.commons.domain.utils;

public class Strings {
  private Strings() {
  }

  public static boolean isBlank(String str) {
    return str == null || str.trim().isEmpty();
  }

  public static boolean isNullOrEmpty(String str) {
    return str == null || str.isEmpty();
  }

  public static boolean isNotNullOrEmpty(String str) {
    return !isNullOrEmpty(str);
  }

  public static String join(String delimiter, long... elements) {
    if (elements == null || elements.length == 0) {
      return "";
    }
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < elements.length; i++) {
      sb.append(elements[i]);
      // 只有在不是最后一个元素时才追加分隔符
      if (i < elements.length - 1) {
        sb.append(delimiter);
      }
    }
    return sb.toString();
  }
}
