package com.fxiaoke.stone.commons.domain.api;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IncFileUsedQuotaArgs {

  @NotBlank(message = "business cannot be blank")
  private String business;

  @Valid
  @NotNull(message = "items cannot be null")
  @Size(min = 1, max = 100, message = "items size must be between 1 and 100")
  private List<IncFileUsedQuotaItem> items;
}
