package com.fxiaoke.stone.commons.domain.api;

import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GenerateFileUploadSignature {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg {
    @NotBlank
    String tenantId;

    @NotNull
    @Min(60)
    Long expireTime;

    @NotNull
    FileResourceEnum resourceType;

    boolean isStreamUpload;

    String filename;

    String extension;

    @NotNull
    @Min(1)
    Integer fileSize;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result{
    // 文件上传请求方法类型 如POST
    String method;
    // 文件上传地址 如 https://img.ceshi112.com/FilesOne
    String url;

    // 以下参数均需在Head中传递,参数KEY为下列属性名且首字母需大写，Value为下列属性的值
    // 如：Acid:71554.1000
    // 如：Type:C

    // 上传企业EA.Ei
    String acid;
    // 要上传的文件类型，如C、TC、N、TN
    String resource;
    // 签名accessKey
    String ak;
    // 文件上传签名
    String sign;
    // 签名过期时间戳
    long expiry;
    // 经过URLEncode编码过的文件名
    String filename;
    // 文件大小，单位字节
    int size;
    // 参数及签名的摘要
    String digest;

    String contentType="multipart/form-data";
  }
}
