package com.fxiaoke.stone.commons.domain.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class FilenameUtil {
  private FilenameUtil() {
  }
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");


  // 生成 YYYY-MM-DD-HH.bin 格式的文件名
  public static String generateFileName() {
    LocalDateTime now = LocalDateTime.now();
    return now.format(formatter) + ".bin";
  }

  // 生成 YYYY-MM-DD-HH格式的文件名
  public static String generateFileName(String suffix) {
    LocalDateTime now = LocalDateTime.now();
    return now.format(formatter) + "." + suffix;
  }
  // 对文件名进行URL编码
  public static String encode(String filename) throws UnsupportedEncodingException {
    return URLEncoder.encode(filename, "UTF-8");
  }

  // 获取文件扩展名不包含点号
  public static String getExtension(String filename) {
    if (filename == null || filename.isEmpty()) {
      return "";
    }
    int index = filename.lastIndexOf(".");
    if (index == -1 || index == filename.length() - 1) {
      return "";
    }
    return filename.substring(index + 1);
  }

  // 获取文件扩展名，如果没有扩展名则返回默认扩展名
  public static String getExtension(String filename, String defaultExtension) {
    String extension = getExtension(filename);
    return extension.isEmpty() ? defaultExtension : extension;
  }

  public static String getFirstSeparatorBeforeName(String filename) {
    if (filename == null || filename.isEmpty()) {
      return "";
    }
    int index = filename.indexOf(".");
    if (index == -1) {
      return filename;
    }
    return filename.substring(0, index);
  }

  public static String replaceExtension(String filename, String extension) {
    int index = filename.lastIndexOf(".");
    if (index == -1) {
      return filename + "." + extension;
    }
    return filename.substring(0, index) + "." + extension;
  }
}
