package com.fxiaoke.stone.commons.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class UploadFileOmit {
  // 文件上传请求方法类型 如POST
  String method;
  // 文件上传地址 如 https://img.ceshi112.com/FilesOne
  String url;

  // 以下参数均需在Head中传递,参数KEY为下列属性名且首字母需大写，Value为下列属性的值
  // 如：Acid:71554.1000
  // 如：Type:C

  // 上传企业EA.Ei
  String acid;
  // 要上传的文件类型，如C、TC、N、TN
  String resource;
  // 签名accessKey
  String ak;
  // 文件上传签名
  String sign;
  // 签名过期时间戳
  long expiry;
  // 经过URLEncode编码过的文件名
  String filename;
  // 文件大小，单位字节
  int size;
  // 参数及签名的摘要
  String digest;

  String contentType="multipart/form-data";
}
