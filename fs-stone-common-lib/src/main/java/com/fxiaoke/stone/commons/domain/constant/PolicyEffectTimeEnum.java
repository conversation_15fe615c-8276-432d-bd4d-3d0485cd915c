package com.fxiaoke.stone.commons.domain.constant;

import lombok.Getter;

@Getter
public enum PolicyEffectTimeEnum {
  // 永久有效
  PERMANENT("permanent"),
  // before
  BEFORE("before"),
  // after
  AFTER("after"),
  // between
  BETWEEN("between");
  private final String type;
  PolicyEffectTimeEnum(String type) {
    this.type = type;
  }

  public static PolicyEffectTimeEnum of(String type) {
    for (PolicyEffectTimeEnum value : PolicyEffectTimeEnum.values()) {
      if (value.type.equals(type)) {
        return value;
      }
    }
    throw new IllegalArgumentException("No such type: " + type);
  }

  // 比较type是否相等
  public static boolean match(String type,PolicyEffectTimeEnum effectTimeEnum) {
    return effectTimeEnum.getType().equals(type);
  }
}
