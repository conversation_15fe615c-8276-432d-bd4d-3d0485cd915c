package com.fxiaoke.stone.commons.domain.api;

import com.fxiaoke.stone.commons.domain.model.EffectTime;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GetPolicyByAccessKey {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg{
    @NotBlank(message = "accessKey can not be empty")
    String accessKey;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result {
    boolean effect;
    String effectTimeType;
    EffectTime effectTime;
    String conditions;
    List<String> actions;
    List<String> resource;
    List<String> groups;
    List<String> tourists;
  }

}
