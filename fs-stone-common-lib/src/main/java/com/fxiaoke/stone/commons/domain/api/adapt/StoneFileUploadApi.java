package com.fxiaoke.stone.commons.domain.api.adapt;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface StoneFileUploadApi {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Req {

    /**
     * 企业账号 非空
     */
    private String ea;

    /**
     * 员工ID,系统调用传 -10000 非空
     */
    private Integer employee_id;

    /**
     * 业务线标识 非空
     */
    private String business;

    /**
     * 安全组 传 `XiaokeNetDisk` 则上传为网盘文件 如上传成网盘文件则下载受限不能直接通过Path下载文件
     */
    private String security_group;

    /**
     * 文件扩展名 非空
     */
    private String extension_name;

    /**
     * 有效期（单位：天） 默认 3 天 结合 tempFile 使用
     */
    private Integer expire_day;

    /**
     * 文件大小 不传则先将文件缓存到本地磁盘,获取到文件大小后再上传，如果传入则直接上传 如果传入的 file_size 与实际大小不一致，则会抛出异常
     */
    private Integer file_size;

    /**
     * 文件Hash值 一般为 MD5 值,用于校验文件完整性与防止重复上传
     */
    private String code;

    /**
     * 图片文件是否保持原格式 默认为 false , 转换为 webp格式保存
     */
    private Boolean keep_format;

    /**
     * 是否上传为C文件 默认为 false , 上传为N文件
     */
    private Boolean need_cdn;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Res {

    private String path;
    private Long size;
    private String extensionName;

    /**
     * 图片处理响应信息 仅当上传的文件为图片类型时
     */
    private ImageProcessResponse image_process_response;
  }

  /**
   * 图片处理响应数据结构 包含图片的基本信息和缩略图处理结果
   */
  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class ImageProcessResponse {

    /**
     * 图片宽度（像素）
     */
    private Integer width;

    /**
     * 图片高度（像素）
     */
    private Integer height;

    /**
     * 图片扩展名 可能与原始扩展名不同（例如转换为webp格式时）
     */
    private String extension_name;

    /**
     * 缩略图列表 如果请求中指定了生成缩略图，此字段将包含生成的缩略图信息
     */
    private StoneFileImageThumbnailResponse thumbnail_list;
  }

  /**
   * 缩略图响应数据结构
   */
  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class StoneFileImageThumbnailResponse {

    /**
     * 缩略图文件path
     */
    private String path;
    /**
     * 缩略图高度
     */
    private Integer height;
    /**
     * 缩略图宽度
     */
    private Integer width;
    /**
     * 缩略图大小
     */
    private Long size;

    private String extensionName;
  }
}
