package com.fxiaoke.stone.commons.domain.exception;

import java.util.Arrays;
import lombok.Getter;

@Getter
public class StoneCommonClientException extends RuntimeException{
  private static final String ARGS_NAME = "args:";
  private final String message;
  private final int code;
  public StoneCommonClientException(String message, int code,Object... args) {
    super(message);
    this.code = code;
    this.message = message+"-"+ ARGS_NAME + Arrays.toString(args);
  }

  public StoneCommonClientException(Exception e,String message, int code,Object... args) {
    super(e.getMessage(), e);
    this.code = code;
    this.message = message+"-"+ ARGS_NAME + Arrays.toString(args);
  }
}
