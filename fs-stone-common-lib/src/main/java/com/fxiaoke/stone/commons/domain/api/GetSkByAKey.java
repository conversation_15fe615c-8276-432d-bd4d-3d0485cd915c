package com.fxiaoke.stone.commons.domain.api;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GetSkByAKey {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg{
    @NotBlank(message = "accessKey can not be empty")
    String accessKey;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result{
    String accessKey;
    String secretKey;
  }
}
