package com.fxiaoke.stone.commons.domain.model;

import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import java.time.Instant;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class GeneratorSignUrlBaseArg {

  @Valid
  private AcUser acUser;

  @Valid
  private FileUser fileUser;

  @Valid
  private FileInfo fileInfo;

  @Min(value = 60, message = "Expire time cannot be less than 60 seconds")
  @Max(value = 604800, message = "Expire time cannot exceed 604800 seconds")
  protected long expireTime;

  @NotBlank(message = "Business identifier cannot be empty")
  @Size(max = 24, message = "Business identifier cannot exceed 24 characters")
  private String business;

  // 是否使用全球加速
  private boolean globalAcceleration;

  public long getExpiresTimestamp() {
    Instant currentInstant = Instant.now();
    Instant futureInstant = currentInstant.plusSeconds(getExpireTime());
    return futureInstant.toEpochMilli();
  }

  public void check() {
    if (acUser == null) {
      throw new StoneCommonClientException("Access user cannot be null", 400);
    }
    acUser.check();
    if (fileUser == null) {
      throw new StoneCommonClientException("File user cannot be null", 400);
    }
    fileUser.check();
    if (fileInfo == null) {
      throw new StoneCommonClientException("File info cannot be null", 400);
    }
    fileInfo.check();
    checkExpireTime();
    checkBusiness();
  }

  protected void checkExpireTime() {
    if (expireTime < Constants.MIN_EXPIRE_TIME || expireTime > Constants.LONG_MAX_EXPIRE_TIME) {
      throw new StoneCommonClientException("Expire time must be between " + Constants.MIN_EXPIRE_TIME + " and " + Constants.MAX_EXPIRE_TIME + " seconds", 400);
    }
  }

  private void checkBusiness() {
    if (Strings.isNullOrEmpty(business)) {
      throw new StoneCommonClientException("Business identifier cannot be null or empty", 400);
    }
    if (business.length() > 24) {
      throw new StoneCommonClientException(
          "Business identifier length cannot exceed 24 characters", 400);
    }
  }
}
