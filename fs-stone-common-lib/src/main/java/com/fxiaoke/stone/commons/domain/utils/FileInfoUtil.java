package com.fxiaoke.stone.commons.domain.utils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class FileInfoUtil {

  private static final Map<String, String> FILE_TYPE = new HashMap<>();
  private static final Set<String> COMMON_PREVIEW_TYPE= new HashSet<>();
  private static final Set<String> DOCUMENT_PREVIEW_TYPE= new HashSet<>();
  private static final String APPLICATION_POSTSCRIPT = "application/postscript";
  private static final String APPLICATION_XML = "application/xml";
  private static final String APPLICATION_VND_MS_EXCEL = "application/vnd.ms-excel";
  private static final String APPLICATION_X_DIRECTOR = "application/x-director";
  private static final String APPLICATION_X_FONT = "application/x-font";
  private static final String APPLICATION_X_GTAR = "application/x-gtar";
  private static final String APPLICATION_X_HTTPD_PHP = "application/x-httpd-php";
  private static final String APPLICATION_X_KOAN = "application/x-koan";
  private static final String AUDIO_MPEG = "audio/mpeg";
  private static final String APPLICATION_X_TROFF = "application/x-troff";
  private static final String APPLICATION_X_MAKER = "application/x-maker";
  private static final String APPLICATION_X_MSDOS_PROGRAM = "application/x-msdos-program";
  private static final String APPLICATION_X_QGIS = "application/x-qgis";
  private static final String APPLICATION_X_TRASH = "application/x-trash";
  private static final String AUDIO_OGG = "audio/ogg";
  private static final String AUDIO_X_AIFF = "audio/x-aiff";
  private static final String AUDIO_X_PN_REALAUDIO = "audio/x-pn-realaudio";
  private static final String CHEMICAL_X_CACTVS_BINARY = "chemical/x-cactvs-binary";
  private static final String CHEMICAL_X_GAMESS_INPUT = "chemical/x-gamess-input";
  private static final String CHEMICAL_X_GAUSSIAN_INPUT = "chemical/x-gaussian-input";
  private static final String CHEMICAL_X_MOPAC_INPUT = "chemical/x-mopac-input";
  private static final String IMAGE_JPEG = "image/jpeg";
  private static final String MODEL_MESH = "model/mesh";
  private static final String TEXT_HTML = "text/html";
  private static final String TEXT_PLAIN = "text/plain";
  private static final String TEXT_X_C_SRC = "text/x-c++src";
  private static final String TEXT_X_TEX = "text/x-tex";
  private static final String VIDEO_MPEG = "video/mpeg";
  private static final String TEXT_X_C_HDR = "text/x-c++hdr";
  private static final String AUDIO_MIDI = "audio/midi";

  static {
    FILE_TYPE.put("ez", "application/andrew-inset");
    FILE_TYPE.put("anx", "application/annodex");
    FILE_TYPE.put("atom", "application/atom+xml");
    FILE_TYPE.put("atomcat", "application/atomcat+xml");
    FILE_TYPE.put("atomsrv", "application/atomserv+xml");
    FILE_TYPE.put("lin", "application/bbolin");
    FILE_TYPE.put("cap", "application/cap");
    FILE_TYPE.put("pcap", "application/cap");
    FILE_TYPE.put("cu", "application/cu-seeme");
    FILE_TYPE.put("davmount", "application/davmount+xml");
    FILE_TYPE.put("tsp", "application/dsptype");
    FILE_TYPE.put("es", "application/ecmascript");
    FILE_TYPE.put("spl", "application/futuresplash");
    FILE_TYPE.put("hta", "application/hta");
    FILE_TYPE.put("jar", "application/java-archive");
    FILE_TYPE.put("ser", "application/java-serialized-object");
    FILE_TYPE.put("class", "application/java-vm");
    FILE_TYPE.put("js", "application/javascript");
    FILE_TYPE.put("m3g", "application/m3g");
    FILE_TYPE.put("hqx", "application/mac-binhex40");
    FILE_TYPE.put("cpt", "application/mac-compactpro");
    FILE_TYPE.put("nb", "application/mathematica");
    FILE_TYPE.put("nbp", "application/mathematica");
    FILE_TYPE.put("mdb", "application/msaccess");
    FILE_TYPE.put("doc", "application/msword");
    FILE_TYPE.put("dot", "application/msword");
    FILE_TYPE.put("mxf", "application/mxf");
    FILE_TYPE.put("bin", "application/octet-stream");
    FILE_TYPE.put("oda", "application/oda");
    FILE_TYPE.put("ogx", "application/ogg");
    FILE_TYPE.put("pdf", "application/pdf");
    FILE_TYPE.put("key", "application/pgp-keys");
    FILE_TYPE.put("pgp", "application/pgp-signature");
    FILE_TYPE.put("prf", "application/pics-rules");
    FILE_TYPE.put("ps", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("ai", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("eps", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("epsi", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("epsf", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("eps2", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("eps3", APPLICATION_POSTSCRIPT);
    FILE_TYPE.put("rar", "application/rar");
    FILE_TYPE.put("rdf", "application/rdf+xml");
    FILE_TYPE.put("rss", "application/rss+xml");
    FILE_TYPE.put("rtf", "application/rtf");
    FILE_TYPE.put("smi", "application/smil");
    FILE_TYPE.put("smil", "application/smil");
    FILE_TYPE.put("xhtml", "application/xhtml+xml");
    FILE_TYPE.put("xht", "application/xhtml+xml");
    FILE_TYPE.put("xml", APPLICATION_XML);
    FILE_TYPE.put("xsl", APPLICATION_XML);
    FILE_TYPE.put("xsd", APPLICATION_XML);
    FILE_TYPE.put("xspf", "application/xspf+xml");
    FILE_TYPE.put("zip", "application/zip");
    FILE_TYPE.put("apk", "application/vnd.android.package-archive");
    FILE_TYPE.put("cdy", "application/vnd.cinderella");
    FILE_TYPE.put("kml", "application/vnd.google-earth.kml+xml");
    FILE_TYPE.put("kmz", "application/vnd.google-earth.kmz");
    FILE_TYPE.put("xul", "application/vnd.mozilla.xul+xml");
    FILE_TYPE.put("xls", APPLICATION_VND_MS_EXCEL);
    FILE_TYPE.put("xlb", APPLICATION_VND_MS_EXCEL);
    FILE_TYPE.put("xlt", APPLICATION_VND_MS_EXCEL);
    FILE_TYPE.put("cat", "application/vnd.ms-pki.seccat");
    FILE_TYPE.put("stl", "application/vnd.ms-pki.stl");
    FILE_TYPE.put("ppt", "application/vnd.ms-powerpoint");
    FILE_TYPE.put("pps", "application/vnd.ms-powerpoint");
    FILE_TYPE.put("odc", "application/vnd.oasis.opendocument.chart");
    FILE_TYPE.put("odb", "application/vnd.oasis.opendocument.database");
    FILE_TYPE.put("odf", "application/vnd.oasis.opendocument.formula");
    FILE_TYPE.put("odg", "application/vnd.oasis.opendocument.graphics");
    FILE_TYPE.put("otg", "application/vnd.oasis.opendocument.graphics-template");
    FILE_TYPE.put("odi", "application/vnd.oasis.opendocument.image");
    FILE_TYPE.put("odp", "application/vnd.oasis.opendocument.presentation");
    FILE_TYPE.put("otp", "application/vnd.oasis.opendocument.presentation-template");
    FILE_TYPE.put("ods", "application/vnd.oasis.opendocument.spreadsheet");
    FILE_TYPE.put("ots", "application/vnd.oasis.opendocument.spreadsheet-template");
    FILE_TYPE.put("odt", "application/vnd.oasis.opendocument.text");
    FILE_TYPE.put("odm", "application/vnd.oasis.opendocument.text-master");
    FILE_TYPE.put("ott", "application/vnd.oasis.opendocument.text-template");
    FILE_TYPE.put("oth", "application/vnd.oasis.opendocument.text-web");
    FILE_TYPE.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    FILE_TYPE.put("xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
    FILE_TYPE.put("pptx",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation");
    FILE_TYPE.put("ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow");
    FILE_TYPE.put("potx", "application/vnd.openxmlformats-officedocument.presentationml.template");
    FILE_TYPE.put("docx",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    FILE_TYPE.put("dotx",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.template");
    FILE_TYPE.put("cod", "application/vnd.rim.cod");
    FILE_TYPE.put("mmf", "application/vnd.smaf");
    FILE_TYPE.put("sdc", "application/vnd.stardivision.calc");
    FILE_TYPE.put("sds", "application/vnd.stardivision.chart");
    FILE_TYPE.put("sda", "application/vnd.stardivision.draw");
    FILE_TYPE.put("sdd", "application/vnd.stardivision.impress");
    FILE_TYPE.put("sdf", "application/vnd.stardivision.math");
    FILE_TYPE.put("sdw", "application/vnd.stardivision.writer");
    FILE_TYPE.put("sgl", "application/vnd.stardivision.writer-global");
    FILE_TYPE.put("sxc", "application/vnd.sun.xml.calc");
    FILE_TYPE.put("stc", "application/vnd.sun.xml.calc.template");
    FILE_TYPE.put("sxd", "application/vnd.sun.xml.draw");
    FILE_TYPE.put("std", "application/vnd.sun.xml.draw.template");
    FILE_TYPE.put("sxi", "application/vnd.sun.xml.impress");
    FILE_TYPE.put("sti", "application/vnd.sun.xml.impress.template");
    FILE_TYPE.put("sxm", "application/vnd.sun.xml.math");
    FILE_TYPE.put("sxw", "application/vnd.sun.xml.writer");
    FILE_TYPE.put("sxg", "application/vnd.sun.xml.writer.global");
    FILE_TYPE.put("stw", "application/vnd.sun.xml.writer.template");
    FILE_TYPE.put("sis", "application/vnd.symbian.install");
    FILE_TYPE.put("vsd", "application/vnd.visio");
    FILE_TYPE.put("wbxml", "application/vnd.wap.wbxml");
    FILE_TYPE.put("wmlc", "application/vnd.wap.wmlc");
    FILE_TYPE.put("wmlsc", "application/vnd.wap.wmlscriptc");
    FILE_TYPE.put("wpd", "application/vnd.wordperfect");
    FILE_TYPE.put("wp5", "application/vnd.wordperfect5.1");
    FILE_TYPE.put("wk", "application/x-123");
    FILE_TYPE.put("7z", "application/x-7z-compressed");
    FILE_TYPE.put("abw", "application/x-abiword");
    FILE_TYPE.put("dmg", "application/x-apple-diskimage");
    FILE_TYPE.put("bcpio", "application/x-bcpio");
    FILE_TYPE.put("torrent", "application/x-bittorrent");
    FILE_TYPE.put("cab", "application/x-cab");
    FILE_TYPE.put("cbr", "application/x-cbr");
    FILE_TYPE.put("cbz", "application/x-cbz");
    FILE_TYPE.put("cdf", "application/x-cdf");
    FILE_TYPE.put("cda", "application/x-cdf");
    FILE_TYPE.put("vcd", "application/x-cdlink");
    FILE_TYPE.put("pgn", "application/x-chess-pgn");
    FILE_TYPE.put("cpio", "application/x-cpio");
    FILE_TYPE.put("csh", "application/x-csh");
    FILE_TYPE.put("deb", "application/x-debian-package");
    FILE_TYPE.put("udeb", "application/x-debian-package");
    FILE_TYPE.put("dcr", APPLICATION_X_DIRECTOR);
    FILE_TYPE.put("dir", APPLICATION_X_DIRECTOR);
    FILE_TYPE.put("dxr", APPLICATION_X_DIRECTOR);
    FILE_TYPE.put("dms", "application/x-dms");
    FILE_TYPE.put("wad", "application/x-doom");
    FILE_TYPE.put("dvi", "application/x-dvi");
    FILE_TYPE.put("rhtml", "application/x-httpd-eruby");
    FILE_TYPE.put("pfa", APPLICATION_X_FONT);
    FILE_TYPE.put("pfb", APPLICATION_X_FONT);
    FILE_TYPE.put("gsf", APPLICATION_X_FONT);
    FILE_TYPE.put("pcf", APPLICATION_X_FONT);
    FILE_TYPE.put("pcf.Z", APPLICATION_X_FONT);
    FILE_TYPE.put("mm", "application/x-freemind");
    FILE_TYPE.put("gnumeric", "application/x-gnumeric");
    FILE_TYPE.put("sgf", "application/x-go-sgf");
    FILE_TYPE.put("gcf", "application/x-graphing-calculator");
    FILE_TYPE.put("gtar", APPLICATION_X_GTAR);
    FILE_TYPE.put("tgz", APPLICATION_X_GTAR);
    FILE_TYPE.put("taz", APPLICATION_X_GTAR);
    FILE_TYPE.put("hdf", "application/x-hdf");
    FILE_TYPE.put("phtml", APPLICATION_X_HTTPD_PHP);
    FILE_TYPE.put("pht", APPLICATION_X_HTTPD_PHP);
    FILE_TYPE.put("php", APPLICATION_X_HTTPD_PHP);
    FILE_TYPE.put("phps", "application/x-httpd-php-source");
    FILE_TYPE.put("php3", "application/x-httpd-php3");
    FILE_TYPE.put("php3p", "application/x-httpd-php3-preprocessed");
    FILE_TYPE.put("php4", "application/x-httpd-php4");
    FILE_TYPE.put("php5", "application/x-httpd-php5");
    FILE_TYPE.put("ica", "application/x-ica");
    FILE_TYPE.put("info", "application/x-info");
    FILE_TYPE.put("ins", "application/x-internet-signup");
    FILE_TYPE.put("isp", "application/x-internet-signup");
    FILE_TYPE.put("iii", "application/x-iphone");
    FILE_TYPE.put("iso", "application/x-iso9660-image");
    FILE_TYPE.put("jam", "application/x-jam");
    FILE_TYPE.put("jnlp", "application/x-java-jnlp-file");
    FILE_TYPE.put("jmz", "application/x-jmol");
    FILE_TYPE.put("chrt", "application/x-kchart");
    FILE_TYPE.put("kil", "application/x-killustrator");
    FILE_TYPE.put("skp", APPLICATION_X_KOAN);
    FILE_TYPE.put("skd", APPLICATION_X_KOAN);
    FILE_TYPE.put("skt", APPLICATION_X_KOAN);
    FILE_TYPE.put("skm", APPLICATION_X_KOAN);
    FILE_TYPE.put("kpr", "application/x-kpresenter");
    FILE_TYPE.put("kpt", "application/x-kpresenter");
    FILE_TYPE.put("ksp", "application/x-kspread");
    FILE_TYPE.put("kwd", "application/x-kword");
    FILE_TYPE.put("kwt", "application/x-kword");
    FILE_TYPE.put("latex", "application/x-latex");
    FILE_TYPE.put("lha", "application/x-lha");
    FILE_TYPE.put("lyx", "application/x-lyx");
    FILE_TYPE.put("lzh", "application/x-lzh");
    FILE_TYPE.put("lzx", "application/x-lzx");
    FILE_TYPE.put("frm", APPLICATION_X_MAKER);
    FILE_TYPE.put("maker", APPLICATION_X_MAKER);
    FILE_TYPE.put("frame", APPLICATION_X_MAKER);
    FILE_TYPE.put("fm", APPLICATION_X_MAKER);
    FILE_TYPE.put("fb", APPLICATION_X_MAKER);
    FILE_TYPE.put("book", APPLICATION_X_MAKER);
    FILE_TYPE.put("fbdoc", APPLICATION_X_MAKER);
    FILE_TYPE.put("mif", "application/x-mif");
    FILE_TYPE.put("wmd", "application/x-ms-wmd");
    FILE_TYPE.put("wmz", "application/x-ms-wmz");
    FILE_TYPE.put("com", APPLICATION_X_MSDOS_PROGRAM);
    FILE_TYPE.put("exe", APPLICATION_X_MSDOS_PROGRAM);
    FILE_TYPE.put("bat", APPLICATION_X_MSDOS_PROGRAM);
    FILE_TYPE.put("dll", APPLICATION_X_MSDOS_PROGRAM);
    FILE_TYPE.put("msi", "application/x-msi");
    FILE_TYPE.put("nc", "application/x-netcdf");
    FILE_TYPE.put("pac", "application/x-ns-proxy-autoconfig");
    FILE_TYPE.put("dat", "application/x-ns-proxy-autoconfig");
    FILE_TYPE.put("nwc", "application/x-nwc");
    FILE_TYPE.put("o", "application/x-object");
    FILE_TYPE.put("oza", "application/x-oz-application");
    FILE_TYPE.put("p7r", "application/x-pkcs7-certreqresp");
    FILE_TYPE.put("crl", "application/x-pkcs7-crl");
    FILE_TYPE.put("pyc", "application/x-python-code");
    FILE_TYPE.put("pyo", "application/x-python-code");
    FILE_TYPE.put("qgs", APPLICATION_X_QGIS);
    FILE_TYPE.put("shp", APPLICATION_X_QGIS);
    FILE_TYPE.put("shx", APPLICATION_X_QGIS);
    FILE_TYPE.put("qtl", "application/x-quicktimeplayer");
    FILE_TYPE.put("rpm", "application/x-redhat-package-manager");
    FILE_TYPE.put("rb", "application/x-ruby");
    FILE_TYPE.put("sh", "application/x-sh");
    FILE_TYPE.put("shar", "application/x-shar");
    FILE_TYPE.put("swf", "application/x-shockwave-flash");
    FILE_TYPE.put("swfl", "application/x-shockwave-flash");
    FILE_TYPE.put("scr", "application/x-silverlight");
    FILE_TYPE.put("sit", "application/x-stuffit");
    FILE_TYPE.put("sitx", "application/x-stuffit");
    FILE_TYPE.put("sv4cpio", "application/x-sv4cpio");
    FILE_TYPE.put("sv4crc", "application/x-sv4crc");
    FILE_TYPE.put("tar", "application/x-tar");
    FILE_TYPE.put("tcl", "application/x-tcl");
    FILE_TYPE.put("gf", "application/x-tex-gf");
    FILE_TYPE.put("pk", "application/x-tex-pk");
    FILE_TYPE.put("texinfo", "application/x-texinfo");
    FILE_TYPE.put("texi", "application/x-texinfo");
    FILE_TYPE.put("~", APPLICATION_X_TRASH);
    FILE_TYPE.put("%", APPLICATION_X_TRASH);
    FILE_TYPE.put("bak", APPLICATION_X_TRASH);
    FILE_TYPE.put("old", APPLICATION_X_TRASH);
    FILE_TYPE.put("sik", APPLICATION_X_TRASH);
    FILE_TYPE.put("t", APPLICATION_X_TROFF);
    FILE_TYPE.put("tr", APPLICATION_X_TROFF);
    FILE_TYPE.put("roff", APPLICATION_X_TROFF);
    FILE_TYPE.put("man", "application/x-troff-man");
    FILE_TYPE.put("me", "application/x-troff-me");
    FILE_TYPE.put("ms", "application/x-troff-ms");
    FILE_TYPE.put("ustar", "application/x-ustar");
    FILE_TYPE.put("src", "application/x-wais-source");
    FILE_TYPE.put("wz", "application/x-wingz");
    FILE_TYPE.put("crt", "application/x-x509-ca-cert");
    FILE_TYPE.put("xcf", "application/x-xcf");
    FILE_TYPE.put("fig", "application/x-xfig");
    FILE_TYPE.put("xpi", "application/x-xpinstall");
    FILE_TYPE.put("amr", "audio/amr");
    FILE_TYPE.put("awb", "audio/amr-wb");
    FILE_TYPE.put("axa", "audio/annodex");
    FILE_TYPE.put("au", "audio/basic");
    FILE_TYPE.put("snd", "audio/basic");
    FILE_TYPE.put("flac", "audio/flac");
    FILE_TYPE.put("mid", AUDIO_MIDI);
    FILE_TYPE.put("midi", AUDIO_MIDI);
    FILE_TYPE.put("kar", AUDIO_MIDI);
    FILE_TYPE.put("mpga", AUDIO_MPEG);
    FILE_TYPE.put("mpega", AUDIO_MPEG);
    FILE_TYPE.put("mp2", AUDIO_MPEG);
    FILE_TYPE.put("mp3", AUDIO_MPEG);
    FILE_TYPE.put("m4a", AUDIO_MPEG);
    FILE_TYPE.put("m3u", "audio/mpegurl");
    FILE_TYPE.put("oga", AUDIO_OGG);
    FILE_TYPE.put("ogg", AUDIO_OGG);
    FILE_TYPE.put("spx", AUDIO_OGG);
    FILE_TYPE.put("sid", "audio/prs.sid");
    FILE_TYPE.put("aif", AUDIO_X_AIFF);
    FILE_TYPE.put("aiff", AUDIO_X_AIFF);
    FILE_TYPE.put("aifc", AUDIO_X_AIFF);
    FILE_TYPE.put("gsm", "audio/x-gsm");
    FILE_TYPE.put("wma", "audio/x-ms-wma");
    FILE_TYPE.put("wax", "audio/x-ms-wax");
    FILE_TYPE.put("ra", AUDIO_X_PN_REALAUDIO);
    FILE_TYPE.put("rm", AUDIO_X_PN_REALAUDIO);
    FILE_TYPE.put("ram", AUDIO_X_PN_REALAUDIO);
    FILE_TYPE.put("pls", "audio/x-scpls");
    FILE_TYPE.put("sd2", "audio/x-sd2");
    FILE_TYPE.put("wav", "audio/x-wav");
    FILE_TYPE.put("alc", "chemical/x-alchemy");
    FILE_TYPE.put("cac", "chemical/x-cache");
    FILE_TYPE.put("cache", "chemical/x-cache");
    FILE_TYPE.put("csf", "chemical/x-cache-csf");
    FILE_TYPE.put("cbin", CHEMICAL_X_CACTVS_BINARY);
    FILE_TYPE.put("cascii", CHEMICAL_X_CACTVS_BINARY);
    FILE_TYPE.put("ctab", CHEMICAL_X_CACTVS_BINARY);
    FILE_TYPE.put("cdx", "chemical/x-cdx");
    FILE_TYPE.put("cer", "chemical/x-cerius");
    FILE_TYPE.put("c3d", "chemical/x-chem3d");
    FILE_TYPE.put("chm", "chemical/x-chemdraw");
    FILE_TYPE.put("cif", "chemical/x-cif");
    FILE_TYPE.put("cmdf", "chemical/x-cmdf");
    FILE_TYPE.put("cml", "chemical/x-cml");
    FILE_TYPE.put("cpa", "chemical/x-compass");
    FILE_TYPE.put("bsd", "chemical/x-crossfire");
    FILE_TYPE.put("csml", "chemical/x-csml");
    FILE_TYPE.put("csm", "chemical/x-csml");
    FILE_TYPE.put("ctx", "chemical/x-ctx");
    FILE_TYPE.put("cxf", "chemical/x-cxf");
    FILE_TYPE.put("cef", "chemical/x-cxf");
    FILE_TYPE.put("emb", "chemical/x-embl-dl-nucleotide");
    FILE_TYPE.put("embl", "chemical/x-embl-dl-nucleotide");
    FILE_TYPE.put("spc", "chemical/x-galactic-spc");
    FILE_TYPE.put("inp", CHEMICAL_X_GAMESS_INPUT);
    FILE_TYPE.put("gam", CHEMICAL_X_GAMESS_INPUT);
    FILE_TYPE.put("gamin", CHEMICAL_X_GAMESS_INPUT);
    FILE_TYPE.put("fch", "chemical/x-gaussian-checkpoint");
    FILE_TYPE.put("fchk", "chemical/x-gaussian-checkpoint");
    FILE_TYPE.put("cub", "chemical/x-gaussian-cube");
    FILE_TYPE.put("gau", CHEMICAL_X_GAUSSIAN_INPUT);
    FILE_TYPE.put("gjc", CHEMICAL_X_GAUSSIAN_INPUT);
    FILE_TYPE.put("gjf", CHEMICAL_X_GAUSSIAN_INPUT);
    FILE_TYPE.put("gal", "chemical/x-gaussian-log");
    FILE_TYPE.put("gcg", "chemical/x-gcg8-sequence");
    FILE_TYPE.put("gen", "chemical/x-genbank");
    FILE_TYPE.put("hin", "chemical/x-hin");
    FILE_TYPE.put("istr", "chemical/x-isostar");
    FILE_TYPE.put("ist", "chemical/x-isostar");
    FILE_TYPE.put("jdx", "chemical/x-jcamp-dx");
    FILE_TYPE.put("dx", "chemical/x-jcamp-dx");
    FILE_TYPE.put("kin", "chemical/x-kinemage");
    FILE_TYPE.put("mcm", "chemical/x-macmolecule");
    FILE_TYPE.put("mmd", "chemical/x-macromodel-input");
    FILE_TYPE.put("mmod", "chemical/x-macromodel-input");
    FILE_TYPE.put("mol", "chemical/x-mdl-molfile");
    FILE_TYPE.put("rd", "chemical/x-mdl-rdfile");
    FILE_TYPE.put("rxn", "chemical/x-mdl-rxnfile");
    FILE_TYPE.put("sd", "chemical/x-mdl-sdfile");
    FILE_TYPE.put("tgf", "chemical/x-mdl-tgf");
    FILE_TYPE.put("mcif", "chemical/x-mmcif");
    FILE_TYPE.put("mol2", "chemical/x-mol2");
    FILE_TYPE.put("b", "chemical/x-molconn-Z");
    FILE_TYPE.put("gpt", "chemical/x-mopac-graph");
    FILE_TYPE.put("mop", CHEMICAL_X_MOPAC_INPUT);
    FILE_TYPE.put("mopcrt", CHEMICAL_X_MOPAC_INPUT);
    FILE_TYPE.put("mpc", CHEMICAL_X_MOPAC_INPUT);
    FILE_TYPE.put("zmt", CHEMICAL_X_MOPAC_INPUT);
    FILE_TYPE.put("moo", "chemical/x-mopac-out");
    FILE_TYPE.put("mvb", "chemical/x-mopac-vib");
    FILE_TYPE.put("asn", "chemical/x-ncbi-asn1");
    FILE_TYPE.put("prt", "chemical/x-ncbi-asn1-ascii");
    FILE_TYPE.put("ent", "chemical/x-ncbi-asn1-ascii");
    FILE_TYPE.put("val", "chemical/x-ncbi-asn1-binary");
    FILE_TYPE.put("aso", "chemical/x-ncbi-asn1-binary");
    FILE_TYPE.put("pdb", "chemical/x-pdb");
    FILE_TYPE.put("ros", "chemical/x-rosdal");
    FILE_TYPE.put("sw", "chemical/x-swissprot");
    FILE_TYPE.put("vms", "chemical/x-vamas-iso14976");
    FILE_TYPE.put("vmd", "chemical/x-vmd");
    FILE_TYPE.put("xtel", "chemical/x-xtel");
    FILE_TYPE.put("xyz", "chemical/x-xyz");
    FILE_TYPE.put("bmp", "image/bmp");
    FILE_TYPE.put("gif", "image/gif");
    FILE_TYPE.put("ief", "image/ief");
    FILE_TYPE.put("jpeg", IMAGE_JPEG);
    FILE_TYPE.put("jpg", IMAGE_JPEG);
    FILE_TYPE.put("webp", "image/webp");
    FILE_TYPE.put("jpe", IMAGE_JPEG);
    FILE_TYPE.put("pcx", "image/pcx");
    FILE_TYPE.put("png", "image/png");
    FILE_TYPE.put("svg", "image/svg+xml");
    FILE_TYPE.put("svgz", "image/svg+xml");
    FILE_TYPE.put("tiff", "image/tiff");
    FILE_TYPE.put("tif", "image/tiff");
    FILE_TYPE.put("djvu", "image/vnd.djvu");
    FILE_TYPE.put("djv", "image/vnd.djvu");
    FILE_TYPE.put("wbmp", "image/vnd.wap.wbmp");
    FILE_TYPE.put("cr2", "image/x-canon-cr2");
    FILE_TYPE.put("crw", "image/x-canon-crw");
    FILE_TYPE.put("ras", "image/x-cmu-raster");
    FILE_TYPE.put("cdr", "image/x-coreldraw");
    FILE_TYPE.put("pat", "image/x-coreldrawpattern");
    FILE_TYPE.put("cdt", "image/x-coreldrawtemplate");
    FILE_TYPE.put("erf", "image/x-epson-erf");
    FILE_TYPE.put("ico", "image/x-icon");
    FILE_TYPE.put("art", "image/x-jg");
    FILE_TYPE.put("jng", "image/x-jng");
    FILE_TYPE.put("nef", "image/x-nikon-nef");
    FILE_TYPE.put("orf", "image/x-olympus-orf");
    FILE_TYPE.put("psd", "image/x-photoshop");
    FILE_TYPE.put("pnm", "image/x-portable-anymap");
    FILE_TYPE.put("pbm", "image/x-portable-bitmap");
    FILE_TYPE.put("pgm", "image/x-portable-graymap");
    FILE_TYPE.put("ppm", "image/x-portable-pixmap");
    FILE_TYPE.put("rgb", "image/x-rgb");
    FILE_TYPE.put("xbm", "image/x-xbitmap");
    FILE_TYPE.put("xpm", "image/x-xpixmap");
    FILE_TYPE.put("xwd", "image/x-xwindowdump");
    FILE_TYPE.put("eml", "message/rfc822");
    FILE_TYPE.put("igs", "model/iges");
    FILE_TYPE.put("iges", "model/iges");
    FILE_TYPE.put("msh", MODEL_MESH);
    FILE_TYPE.put("mesh", MODEL_MESH);
    FILE_TYPE.put("silo", MODEL_MESH);
    FILE_TYPE.put("wrl", "model/vrml");
    FILE_TYPE.put("vrml", "model/vrml");
    FILE_TYPE.put("x3dv", "model/x3d+vrml");
    FILE_TYPE.put("x3d", "model/x3d+xml");
    FILE_TYPE.put("x3db", "model/x3d+binary");
    FILE_TYPE.put("manifest", "text/cache-manifest");
    FILE_TYPE.put("ics", "text/calendar");
    FILE_TYPE.put("icz", "text/calendar");
    FILE_TYPE.put("css", "text/css");
    FILE_TYPE.put("csv", "text/csv");
    FILE_TYPE.put("323", "text/h323");
    FILE_TYPE.put("html", TEXT_HTML);
    FILE_TYPE.put("htm", TEXT_HTML);
    FILE_TYPE.put("shtml", TEXT_HTML);
    FILE_TYPE.put("uls", "text/iuls");
    FILE_TYPE.put("mml", "text/mathml");
    FILE_TYPE.put("asc", TEXT_PLAIN);
    FILE_TYPE.put("txt", TEXT_PLAIN);
    FILE_TYPE.put("text", TEXT_PLAIN);
    FILE_TYPE.put("pot", TEXT_PLAIN);
    FILE_TYPE.put("brf", TEXT_PLAIN);
    FILE_TYPE.put("rtx", "text/richtext");
    FILE_TYPE.put("sct", "text/scriptlet");
    FILE_TYPE.put("wsc", "text/scriptlet");
    FILE_TYPE.put("tm", "text/texmacs");
    FILE_TYPE.put("ts", "text/texmacs");
    FILE_TYPE.put("tsv", "text/tab-separated-values");
    FILE_TYPE.put("jad", "text/vnd.sun.j2me.app-descriptor");
    FILE_TYPE.put("wml", "text/vnd.wap.wml");
    FILE_TYPE.put("wmls", "text/vnd.wap.wmlscript");
    FILE_TYPE.put("bib", "text/x-bibtex");
    FILE_TYPE.put("boo", "text/x-boo");
    FILE_TYPE.put("h++", TEXT_X_C_HDR);
    FILE_TYPE.put("hpp", TEXT_X_C_HDR);
    FILE_TYPE.put("hxx", TEXT_X_C_HDR);
    FILE_TYPE.put("hh", TEXT_X_C_HDR);
    FILE_TYPE.put("c++", TEXT_X_C_SRC);
    FILE_TYPE.put("cpp", TEXT_X_C_SRC);
    FILE_TYPE.put("cxx", TEXT_X_C_SRC);
    FILE_TYPE.put("cc", TEXT_X_C_SRC);
    FILE_TYPE.put("h", "text/x-chdr");
    FILE_TYPE.put("htc", "text/x-component");
    FILE_TYPE.put("c", "text/x-csrc");
    FILE_TYPE.put("d", "text/x-dsrc");
    FILE_TYPE.put("diff", "text/x-diff");
    FILE_TYPE.put("patch", "text/x-diff");
    FILE_TYPE.put("hs", "text/x-haskell");
    FILE_TYPE.put("java", "text/x-java");
    FILE_TYPE.put("lhs", "text/x-literate-haskell");
    FILE_TYPE.put("moc", "text/x-moc");
    FILE_TYPE.put("p", "text/x-pascal");
    FILE_TYPE.put("pas", "text/x-pascal");
    FILE_TYPE.put("gcd", "text/x-pcs-gcd");
    FILE_TYPE.put("pl", "text/x-perl");
    FILE_TYPE.put("pm", "text/x-perl");
    FILE_TYPE.put("py", "text/x-python");
    FILE_TYPE.put("scala", "text/x-scala");
    FILE_TYPE.put("etx", "text/x-setext");
    FILE_TYPE.put("tk", "text/x-tcl");
    FILE_TYPE.put("tex", TEXT_X_TEX);
    FILE_TYPE.put("ltx", TEXT_X_TEX);
    FILE_TYPE.put("sty", TEXT_X_TEX);
    FILE_TYPE.put("cls", TEXT_X_TEX);
    FILE_TYPE.put("vcs", "text/x-vcalendar");
    FILE_TYPE.put("vcf", "text/x-vcard");
    FILE_TYPE.put("3gp", "video/3gpp");
    FILE_TYPE.put("axv", "video/annodex");
    FILE_TYPE.put("dl", "video/dl");
    FILE_TYPE.put("dif", "video/dv");
    FILE_TYPE.put("dv", "video/dv");
    FILE_TYPE.put("fli", "video/fli");
    FILE_TYPE.put("gl", "video/gl");
    FILE_TYPE.put("mpeg", VIDEO_MPEG);
    FILE_TYPE.put("mpg", VIDEO_MPEG);
    FILE_TYPE.put("mpe", VIDEO_MPEG);
    FILE_TYPE.put("mp4", "video/mp4");
    FILE_TYPE.put("qt", "video/quicktime");
    FILE_TYPE.put("mov", "video/quicktime");
    FILE_TYPE.put("ogv", "video/ogg");
    FILE_TYPE.put("mxu", "video/vnd.mpegurl");
    FILE_TYPE.put("flv", "video/x-flv");
    FILE_TYPE.put("lsf", "video/x-la-asf");
    FILE_TYPE.put("lsx", "video/x-la-asf");
    FILE_TYPE.put("mng", "video/x-mng");
    FILE_TYPE.put("asf", "video/x-ms-asf");
    FILE_TYPE.put("asx", "video/x-ms-asf");
    FILE_TYPE.put("wm", "video/x-ms-wm");
    FILE_TYPE.put("wmv", "video/x-ms-wmv");
    FILE_TYPE.put("wmx", "video/x-ms-wmx");
    FILE_TYPE.put("wvx", "video/x-ms-wvx");
    FILE_TYPE.put("avi", "video/x-msvideo");
    FILE_TYPE.put("movie", "video/x-sgi-movie");
    FILE_TYPE.put("mpv", "video/x-matroska");
    FILE_TYPE.put("mkv", "video/x-matroska");
    FILE_TYPE.put("ice", "x-conference/x-cooltalk");
    FILE_TYPE.put("sisx", "x-epoc/x-sisx-app");
    FILE_TYPE.put("vrm", "x-world/x-vrml");

    COMMON_PREVIEW_TYPE.add("jpg");
    COMMON_PREVIEW_TYPE.add("png");
    COMMON_PREVIEW_TYPE.add("jpeg");
    COMMON_PREVIEW_TYPE.add("webp");
    COMMON_PREVIEW_TYPE.add("bmp");
    COMMON_PREVIEW_TYPE.add("gif");
    COMMON_PREVIEW_TYPE.add("txt");
    COMMON_PREVIEW_TYPE.add("sql");
    COMMON_PREVIEW_TYPE.add("js");
    COMMON_PREVIEW_TYPE.add("css");
    COMMON_PREVIEW_TYPE.add("csv");
    COMMON_PREVIEW_TYPE.add("json");
    COMMON_PREVIEW_TYPE.add("md");
    COMMON_PREVIEW_TYPE.add("xml");
    COMMON_PREVIEW_TYPE.add("py");
    COMMON_PREVIEW_TYPE.add("java");
    COMMON_PREVIEW_TYPE.add("mp3");
    COMMON_PREVIEW_TYPE.add("mp4");

    DOCUMENT_PREVIEW_TYPE.add("pdf");
    DOCUMENT_PREVIEW_TYPE.add("doc");
    DOCUMENT_PREVIEW_TYPE.add("docx");
    DOCUMENT_PREVIEW_TYPE.add("xls");
    DOCUMENT_PREVIEW_TYPE.add("xlsx");
    DOCUMENT_PREVIEW_TYPE.add("ppt");
    DOCUMENT_PREVIEW_TYPE.add("pptx");
  }

  /**
   * 根据文件后缀获取文件类型
   *
   * @param extension 文件后缀 如：txt、doc、xls
   * @return 文件类型 已知类型 返回如 text/plain、application/msword、application/vnd.ms-excel,
   * 未知类型或extension为空 返回 application/octet-stream
   */
  public static String getMimeTypeByExtension(String extension) {
    if (extension != null && !extension.isEmpty()) {
      String type = FILE_TYPE.get(extension);
      if (type != null) {
        return type;
      }
    }
    return "application/octet-stream";
  }

  public static boolean isImageByExtension(String extension) {
    if (extension != null && !extension.isEmpty()) {
      String type = FILE_TYPE.get(extension);
      if (type != null) {
        return type.startsWith("image");
      }
    }
    return false;
  }

  public static boolean isExtensionMatchingMimeType(String extension, String mimeType) {
    if (extension != null && !extension.isEmpty() && mimeType != null && !mimeType.isEmpty()) {
      String type = FILE_TYPE.get(extension);
      if (type != null) {
        return type.equals(mimeType);
      }
    }
    return false;
  }

  private static final String SUPPORT_IMAGE_EXTENSION = "jpg,jpeg,png,gif,bmp,webp";
  public static boolean isSupportImageByExtension(String extension) {
    if (extension != null && !extension.isEmpty()) {
      return SUPPORT_IMAGE_EXTENSION.contains(extension);
    }
    return false;
  }

  private static final String SUPPORT_IMAGE_MIME_TYPE = "image/jpeg,image/png,image/gif,image/bmp,image/webp";

  public static boolean isSupportImageByMimeType(String mimeType) {
    if (mimeType != null && !mimeType.isEmpty()) {
      return SUPPORT_IMAGE_MIME_TYPE.contains(mimeType);
    }
    return false;
  }

  public static boolean isSupportCommonPreview(String extension) {
    if (extension != null && !extension.isEmpty()) {
      return COMMON_PREVIEW_TYPE.contains(extension);
    }
    return false;
  }

  public static boolean isSupportDocumentPreview(String extension) {
    if (extension != null && !extension.isEmpty()) {
      return DOCUMENT_PREVIEW_TYPE.contains(extension);
    }
    return false;
  }

}
