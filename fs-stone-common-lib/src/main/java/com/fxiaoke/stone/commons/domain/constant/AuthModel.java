package com.fxiaoke.stone.commons.domain.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 认证模式<br>
 * - sign:仅认证签名(适合URL下发后实时触发的场景)<br>
 * - cookie: 根据传入的用户信息自动区分上下游企业,一旦生成URL即会锁死认证模式,生成的URL在上下游不通用<br>
 * - cookie_all: 在生成URL阶段不区分上下游,生成的URL上下游通用,但效率较低,权限为企业级不能聚焦到个人<br>
 * - sign_cookie:优先认证签名,签名过期候补认证cookie(适合URL下发后触发具有延迟性的场景)<br>
 */
@Getter
@AllArgsConstructor
public enum AuthModel {
  SIGN("sign"),
  SIGN_COOKIE("sign_cookie"),
  COOKIE("cookie"),
  COOKIE_ALL("cookie_all");
  private final String model;
}
