package com.fxiaoke.stone.commons.domain.api;


import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IncFileUsedQuotaArg {
  @NotBlank(message = "business cannot be blank")
  private String business;

  @Valid
  @NotNull(message = "item cannot be null")
  private IncFileUsedQuotaItem item;
}
