package com.fxiaoke.stone.commons.domain.model;

import java.io.Serializable;

public class Pair<F, S> implements Serializable {
  private static final long serialVersionUID = 1L;
  public final F first;
  public final S second;

  public Pair(F first, S second) {
    this.first = first;
    this.second = second;
  }

  private static boolean eq(Object a, Object b) {
    return (a == b) || (a != null && a.equals(b));
  }

  /**
   * 通过值创建值对
   *
   * @param f 第一个值
   * @param s 第二个值
   * @return 值对
   */
  public static <F, S> Pair<F, S> build(F f, S s) {
    return new Pair<>(f, s);
  }

  /**
   * 通过值创建值对
   *
   * @param f 第一个值
   * @param s 第二个值
   * @return 值对
   */
  public static <F, S> Pair<F, S> of(F f, S s) {
    return new Pair<>(f, s);
  }

  public F getKey() {
    return first;
  }

  public S getValue() {
    return second;
  }

  public F getT1() {
    return first;
  }

  public S getT2() {
    return second;
  }

  @Override
  public int hashCode() {
    return 17 * ((first != null) ? first.hashCode() : 0) + 17 * ((second != null) ? second.hashCode() : 0);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (!(o instanceof Pair<?, ?>)) {
      return false;
    }
    Pair<?, ?> that = (Pair<?, ?>) o;
    return eq(this.first, that.first) && eq(this.second, that.second);
  }

  @Override
  public String toString() {
    return String.format("(%s,%s)", first, second);
  }
}
