package com.fxiaoke.stone.commons.domain.api;

import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GetPresignedCredentialsAsk {


  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg {
    @Min(value = 1, message = "enterpriseId can not be less than 1")
    @NotNull(message = "enterpriseId can not be empty")
    Long enterpriseId;

    @NotBlank(message = "business can not be empty")
    String business;

    //判断business的值是否在businessEnum中
    @AssertTrue(message = "business is invalid")
    public boolean isValidBusiness(){
      for (BusinessEnum businessEnum : BusinessEnum.values()) {
        if (businessEnum.getBusiness().equals(business)) {
          return true;
        }
      }
      return false;
    }
  }
  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result{
    String accessKey;
    String secretKey;
  }
}
