package com.fxiaoke.stone.commons.domain.model;

import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import javax.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 文件用户类（即 文件所属者）<br>
 * tenantId: 租户ID <br>
 * userId: 用户ID (可以为空)<br>
 * securityGroup: 安全组 (可以为空）<br>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class FileUser {

  @Min(value = 1, message = "tenantId must be greater than 0")
  private long tenantId;

  private long userId;

  private String securityGroup;

  public String getDefaultSecurityGroup(){
    if (securityGroup == null) {
      return "";
    }
    return securityGroup;
  }

  public String getTenantIdStr() {
    return String.valueOf(tenantId);
  }

  public void check() {
    if (tenantId <= 0) {
      throw new StoneCommonClientException("FileUser.tenantId must be greater than 0", 400);
    }
  }
}
