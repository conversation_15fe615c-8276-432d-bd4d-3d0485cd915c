package com.fxiaoke.stone.commons.domain.model;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ImmOmit implements Serializable {
  String projectName;
  String accessKey;
  String secretKey;
  String bucketName;
}
