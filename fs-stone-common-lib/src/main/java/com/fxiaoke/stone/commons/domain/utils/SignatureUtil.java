package com.fxiaoke.stone.commons.domain.utils;


import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class SignatureUtil {

  private SignatureUtil() {
  }
  // 生成签名 (元数据、appFramework使用)
  public static String getSignatureWithHmacSha1(String key, String input) throws StoneCommonClientException{
    SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), Constants.SIGNATURE_ALGORITHM);
    Mac mac;
    try {
      mac = Mac.getInstance(Constants.SIGNATURE_ALGORITHM);
      mac.init(keySpec);
    } catch (NoSuchAlgorithmException | InvalidKeyException e) {
      throw new StoneCommonClientException(e,"signature calculation fail",400,key,input);
    }
    byte[] inputBytes = input.getBytes(StandardCharsets.UTF_8);
    byte[] signatureData = mac.doFinal(inputBytes);
    return Base64.getUrlEncoder().encodeToString(signatureData);
  }

  // 验证签名是否正确(文件服务网关使用)
  public static boolean validateSignature(String key, String input, String signature) throws StoneCommonClientException {
    String newSignature = getSignatureWithHmacSha1(key, input);
    return newSignature.equals(signature);
  }

  // 计算消息的MD5摘要，一般用于为请求参数生成签名
  public static String messageDigest(String message) throws StoneCommonClientException {
    try {
      MessageDigest messageDigest = MessageDigest.getInstance("MD5");
      byte[] inputBytes = message.getBytes(StandardCharsets.UTF_8);
      byte[] digest = messageDigest.digest(inputBytes);
      return Base64.getUrlEncoder().encodeToString(digest);
    } catch (Exception e) {
      throw new StoneCommonClientException(e,"failed to compute the message digest",400);
    }
  }

  // 验证消息摘要是否正确
  public static boolean validateMessageDigest(String message,String digest) throws StoneCommonClientException {
    String newDigest = messageDigest(message);
    return newDigest.equals(digest);
  }

  public static String generatorRaw(Object... args) {
    return Joiner.on(Constants.DELIMITER).join(args);
  }

  public static String[] splitRaw(String raw) {
    if (raw == null || raw.isEmpty()) {
      return new String[0];
    }
    return Splitter.on(Constants.DELIMITER).splitToList(raw).toArray(new String[0]);
  }

}
