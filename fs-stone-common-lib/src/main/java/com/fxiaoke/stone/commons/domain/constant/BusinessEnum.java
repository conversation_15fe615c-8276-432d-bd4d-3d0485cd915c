package com.fxiaoke.stone.commons.domain.constant;

import lombok.Getter;

@Getter
public enum BusinessEnum {
  METADATA("metadata"),
  APP_FRAMEWORK("appFramework"),
  MARKETING("marketing"),
  OPEN_API("openApi"),
  FUNCTION("function"),
  STS("sts"),;
  private final String business;
  BusinessEnum(String business) {
    this.business = business;
  }

  public static BusinessEnum of(String business) {
    for (BusinessEnum value : BusinessEnum.values()) {
      if (value.business.equals(business)) {
        return value;
      }
    }
    throw new IllegalArgumentException("This business: "+business+" is not supported to use the current client,"
        + "please contact the developer to register");
  }
}
