package com.fxiaoke.stone.commons.domain.constant;

import lombok.Getter;

@Getter
public enum FileResourceEnum {
  N("N"),
  TN("TN"),
  C("C"),
  TC("TC");
  private final String resource;
  FileResourceEnum(String resources) {
    this.resource = resources;
  }

  public static FileResourceEnum of(String resources) {
    for (FileResourceEnum value : FileResourceEnum.values()) {
      if (value.resource.equals(resources)) {
        return value;
      }
    }
    throw new IllegalArgumentException("No such resources: " + resources);
  }
}
