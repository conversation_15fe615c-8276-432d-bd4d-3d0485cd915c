package com.fxiaoke.stone.commons.domain.utils;

import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.io.InputStream;

public class CodingUtils {

  private CodingUtils() {
    // 私有构造函数，防止实例化
  }

  /**
   * 校验上传文件参数是否合法。
   *
   * @param arg    上传文件的参数
   * @param stream 文件的输入流
   */
  public static void throwIfInvalid(StoneUploadFile.Arg arg, InputStream stream) {

    throwIfNull(arg, "arg");

    throwIfNull(stream, "stream");

    throwIfNull(arg.getResourceType(), "arg.resourceType");

    throwIfBlank(arg.getEa(), "arg.ea");

    throwIfNull(arg.getEmployeeId(), "arg.employeeId");

    throwIfBlank(arg.getBusiness(), "arg.business");

    throwIfBlank(arg.getExtension(), "arg.extension");

    throwIfEmployeeIdInvalid(arg.getEmployeeId(), "arg.employeeId");
  }

  /**
   * 如果对象为 null，则抛出异常。
   *
   * @param object    要检查的对象
   * @param fieldName 字段的业务名称
   */
  public static void throwIfNull(Object object, String fieldName) {
    if (object == null) {
      throw new StoneCommonClientException(fieldName + " cannot be null", 400);
    }
  }

  /**
   * 如果字符串为空白，则抛出异常。
   *
   * @param str       要检查的字符串
   * @param fieldName 字段的业务名称
   * @param args      传递给异常的上下文对象
   */
  public static void throwIfBlank(String str, String fieldName, Object... args) {
    if (Strings.isBlank(str)) {
      throw new StoneCommonClientException(fieldName + " cannot be blank", 400, args);
    }
  }

  /**
   * 校验 Employee ID
   *
   * @param employeeId 要检查的 ID
   * @param args       传递给异常的上下文对象
   */
  public static void throwIfEmployeeIdInvalid(Integer employeeId, Object... args) {
    if (employeeId == null || (employeeId <= 0 && employeeId != -10000)) {
      throw new StoneCommonClientException(
          "Employee ID must be " + -10000 + " or a positive integer", 400, args);
    }
  }
}
