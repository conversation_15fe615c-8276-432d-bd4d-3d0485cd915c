package com.fxiaoke.stone.commons.domain.api;

import java.util.Date;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GetUsedQuota {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg {

    @NotBlank(message = "ea can not be empty")
    String ea;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result {

    /**
     * 企业账号
     */
    private String ea;
    /**
     * 已使用文件存储配额，单位为字节
     */
    private Long usedQuota;
    /**
     * 配额最后更新时间（配额更新有延迟）
     */
    private Date lastUpdate = new Date();
  }
}
