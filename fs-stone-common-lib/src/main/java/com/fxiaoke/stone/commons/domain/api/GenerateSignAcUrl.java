package com.fxiaoke.stone.commons.domain.api;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GenerateSignAcUrl {

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  @ToString(callSuper = true)
  @EqualsAndHashCode(callSuper = true)
  class Arg extends UserChainArg {
    @Pattern(
        regexp = "^(N_|TN_|C_|TC_|TA_|A_).{0,48}$",
        message = "Invalid path"
    )
    @NotBlank
    String path;

    @NotBlank
    String signature;

    @NotNull
    @Min(60)
    Long expireTime;

    String filename;

    String extension;
  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result{
    String path;
    String url;
  }
}
