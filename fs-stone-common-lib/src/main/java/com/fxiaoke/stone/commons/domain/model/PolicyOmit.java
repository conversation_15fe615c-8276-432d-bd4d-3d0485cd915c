package com.fxiaoke.stone.commons.domain.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class PolicyOmit {
  boolean effect;
  String effectTimeType;
  EffectTime effectTime;
  String conditions;
  List<String> actions;
  List<String> resource;
  List<String> groups;
  List<String> tourists;
}
