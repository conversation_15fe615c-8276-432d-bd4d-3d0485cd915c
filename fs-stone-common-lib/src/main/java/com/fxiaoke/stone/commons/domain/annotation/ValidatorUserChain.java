package com.fxiaoke.stone.commons.domain.annotation;

import com.fxiaoke.stone.commons.domain.validator.UserChainConstraintValidator;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UserChainConstraintValidator.class)
public @interface ValidatorUserChain {
  String message() default "The user info is not valid";

  Class<?>[] groups() default { };

  Class<? extends Payload>[] payload() default { };
}
