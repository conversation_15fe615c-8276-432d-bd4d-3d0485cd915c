# fs-stone-commons-client 使用指南 v3.0

## 目录

- [1. 概述](#1-概述)
- [2. 版本说明](#2-版本说明)
- [3. 依赖引入](#3-依赖引入)
- [4. 配置和初始化](#4-配置和初始化)
- [5. API使用说明](#5-api使用说明)
- [6. 客户端使用指南](#6-客户端使用指南)
- [7. 上传功能详解](#7-上传功能详解)
- [8. 常见问题](#8-常见问题)

---

## 1. 概述

### 1.1 产生背景

**业务痛点：**
1. 文件无租户、上下游、1+N 跨企业访问需求增多
2. 文件跨云访问受限于用户VPN、实现业务逻辑及其复杂
3. 海外用户跨地域访问慢，无法使用全球加速
4. 私有云、专属云、混合云域名体系逐渐复杂、自定义域名企业逐渐增多
5. 文件系统接口多、参数杂、文件类型耦合业务意义，业务不敢用、不敢改无所适从
6. 客户越来越高的安全要求
7. 文件的权限日益复杂

### 1.2 方案落地目标

- **统一接入**：业务对文件的访问、下载、预览可以仅使用由业务Server下发的URL来完成
- **简化前端**：前端关注自身页面的展示与行为，不再关注文件系统与业务Server耦合的逻辑与架构设计，让文件回归资源本身

### 1.3 相关资源

- **产生背景详细说明**：https://sharecrm.feishu.cn/wiki/P1tOweSeQiG0tjkhMGlczkp3nUg?from=from_copylink
- **FilesOne URL使用示例**：https://apifox.com/apidoc/shared-170a4df1-9afe-44ed-956b-aa047c6aaecc?pwd=SgUwnS1L

---

## 2. 版本说明

### 2.1 当前版本信息

| 环境 | 版本 | 合入时间 |
|------|------|----------|
| **RELEASE** | 1.7.1-SNAPSHOT | 2025-03-01 00:01 |
| **RC** | 1.7.3-SNAPSHOT | 2025-06-09 13:43 |
| **Alpha** | 1.7.3-SNAPSHOT | 2025-04-22 12:35 |
| **Local** | 1.7.3-SNAPSHOT | 2025-04-22 12:35 |

**最后更新时间**：2025-04-22  
**ownerId**：9356

### 2.2 版本更新历史

#### **1.0.0-SNAPSHOT**（900版本）
- 初始版本

#### **1.1.0-SNAPSHOT**（拟905版本，向后兼容）
新增**无文件元数据签名**生成无身份访问URL的方法：

```java
import com.fxiaoke.stone.commons.SystemPresetClient;
interface SystemPresetClient{
  generateUrl(
      String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String fileTenantId, String filename,
      String extension, long expireTime, String business
  ) throws StoneCommonClientException;
}
```

#### **1.2.0-SNAPSHOT**
- 修改**营销通获取签名上传信息**接口逻辑，改下发地址为主域名（已废弃）

#### **1.3.0-SNAPSHOT**
新增**appFramework生成C|TC类型文件CDN访问URL**的方法：

```java
import com.fxiaoke.stone.commons.SystemPresetClient;
interface SystemPresetClient {
  String generateCFileAccessUrl(
      String tenantId, String userId, String path, String signature,
      String extension, String business
  ) throws StoneCommonClientException;
}
```

#### **1.4.0-SNAPSHOT**
- 兼容AppFramework在生成签名时，下游用户访问upstreamOwnerId可能为空的问题
- 如果upstreamOwnerId为空，将其值重置为-9527，标识该用户为空

#### **1.5.0-SNAPSHOT**
- `generateUploadFileOmit`接口添加针对tenantId参数的校验，校验不通过会抛出异常
- 补充了单元测试
- 升级fs-stone-common-lib版本到1.5.0-SNAPSHOT
- **该版本向后兼容，无破坏性更新**

#### **1.6.3-SNAPSHOT**
- 新增获取阿里云通义听悟AI调用授权信息接口
- **该版本向后兼容，无破坏性更新**

#### **1.6.4-SNAPSHOT**
- 支持根据AuthModel生成不区分上下游的Cookie认证方式

#### **1.6.5-SNAPSHOT**
新增根据认证模式生成签名访问URL的接口：

```java
String generateUrl(
    String tenantId, String userId, String outTenantId, String outUserId, 
    String upstreamOwnerId, AuthModel authModel, String path, String signature, 
    long expireTime, String filename, String extension
) throws StoneCommonClientException;
```

#### **1.6.6-SNAPSHOT**
- 重构签名URL生成及签名计算方式性能优化
- **该版本向后兼容，无破坏性更新**

#### **1.7.0-SNAPSHOT**
添加签名加解密接口，便于业务将签名安全下发到客户端：

```java
/**
 * 签名加密方法(注:不建议缓存加密签名,签名每次加密后都不同)
 * @param said 言(一般是tenantId)
 * @param signature 文件元数据签名
 * @return 加密后的字符串，格式为Enc(加密内容)
 */
Optional<String> encryptSignature(String said, String signature) throws StoneCommonClientException;

/**
 * 加密签名解密方法
 * @param said 用于验证的言(一般是tenantId)
 * @param encCiphertext 加密的签名
 * @return 返回解密后的原始signature
 */
Optional<String> decryptSignature(String said, String encCiphertext) throws StoneCommonClientException;
```

#### **1.7.1-SNAPSHOT**
- `generateUrl`时支持传入加密签名（默认使用当前访问企业tenantId进行解密）

#### **1.7.2-SNAPSHOT**
- client配置文件初始化移除对javax PostConstruct的依赖
- 高版本Spring无需再手动引入tomcat-annotations-api

#### **1.7.3-SNAPSHOT**
- StoneCommonClient的`generatorDownloadUrl`、`generatorPreviewUrl`方法签名最长时间延长至2年

#### **1.7.4-SNAPSHOT**
- StoneAuthClient新增方法getSkBySts，用于获取Sts类型的授权信息
- 无破坏性更新，新增接口，不与之前的逻辑耦合

---

## 3. 依赖引入

### 3.1 推荐方式（跟随父POM）

```xml
<dependency>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-stone-commons-client</artifactId>
</dependency>
```

### 3.2 手动指定版本

```xml
<dependency>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-stone-commons-client</artifactId>
    <version>1.7.3-SNAPSHOT</version>
</dependency>
```

---

## 4. 配置和初始化

### 4.1 Http支持配置

```java
// 该okHttpSupport定义仅做参考，实际使用请自行定义
@Bean
public HttpSupportFactoryBean okHttpSupport() {
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.init();
    return factoryBean;
}
```

### 4.2 Client Bean注册

#### 默认配置（缓存企业ASK凭证5000个）

```java
// 元数据专属
@Bean
public SystemPresetClient metadataExclusiveClient(OkHttpSupport okHttpClientSupport) {
    return new MetadataExclusiveClient(okHttpClientSupport);
}

// appFramework专属
@Bean
public SystemPresetClient appFrameworkExclusiveClient(OkHttpSupport okHttpClientSupport) {
    return new AppFrameworkExclusiveClient(okHttpClientSupport);
}

// 营销通专属
@Bean
public SystemPresetClient marketingExclusiveClient(OkHttpSupport okHttpClientSupport) {
    return new MarketingExclusiveClient(okHttpClientSupport);
}
```

#### 自定义配置（指定缓存数量）

```java
private static final int CACHE_SIZE = 10000;

@Bean
public SystemPresetClient metadataExclusiveClient(OkHttpSupport okHttpClientSupport) {
    return new MetadataExclusiveClient(okHttpClientSupport, CACHE_SIZE);
}

@Bean
public SystemPresetClient appFrameworkExclusiveClient(OkHttpSupport okHttpClientSupport) {
    return new AppFrameworkExclusiveClient(okHttpClientSupport, CACHE_SIZE);
}

@Bean
public SystemPresetClient marketingExclusiveClient(OkHttpSupport okHttpClientSupport) {
    return new MarketingExclusiveClient(okHttpClientSupport, CACHE_SIZE);
}
```

### 4.3 使用注入

```java
@Resource
SystemPresetClient metadataExclusiveClient;

@Resource
SystemPresetClient appFrameworkExclusiveClient;

@Resource
SystemPresetClient marketingExclusiveClient;
```

---

## 5. API使用说明

### 5.1 元数据签名

```java
/**
 * 生成签名 该签名永久有效
 * 作用: 业务在保存元数据时，对文件进行签名
 * 使用场景: 该签名可用于后续下载文件时的鉴权或企业间文件分享
 * @param enterpriseId 企业ID
 * @param path 文件路径
 * @return 签名
 */
String signature(long enterpriseId, String path);
```

### 5.2 生成访问URL（有元数据签名）

#### 基础方法

```java
/**
 * 生成自签名的下载链接
 * 作用: 用以在业务给前端返回文件访问链接
 * @param tenantId 企业ID，发起访问请求的企业ID（上游企业请求）不能为空
 * @param userId 用户ID，发起访问请求的用户ID（上游企业请求）可以为空或null
 * @param outTenantId 下游企业ID，发起访问请求的企业ID（下游企业请求）可以为空或null
 * @param outUserId 下游用户ID，发起访问请求的用户ID（下游企业请求）可以为空或null
 * @param upstreamOwnerId 下游企业的上游负责人ID（下游企业请求）可以为空或null
 * 允许的组合: tenantId+userId、tenantId+upstreamOwnerId+outTenantId+outUserId
 *           当全部参数均不为空时，默认为下游企业请求
 * @param signature 元数据签名
 * @param expireTime 过期时间 最小为60秒 最大为604800秒即7天
 * @param filename 完整文件名 即携带扩展名
 * 注: SDK会自动对文件名进行URL编码，调用方无需关注
 *    SDK会自动处理filename可能为空的情况，默认为YYYY-MM-DD-HH+扩展名
 *    如果文件名包含不支持URL编码的字符同样使用默认文件名
 * @param extensions 文件扩展名 不需要带点号（注: SDK会自动处理extensions可能为空的情况，默认为bin）
 * @return 下载链接
 */
String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, 
    String upstreamOwnerId, String path, String signature, long expireTime, 
    String filename, String extensions) throws StoneCommonClientException;
```

#### 根据认证模式生成URL

```java
import com.fxiaoke.stone.commons.domain.constant.AuthModel;

/**
 * 生成自签名的下载链接（支持跨云）
 * 作用: 用以在业务给前端返回文件访问链接
 * @param authModel 认证模式 {@link AuthModel}
 *                  - sign: 仅认证签名（适合URL下发后实时触发的场景）
 *                  - cookie: 仅认证Cookie-FSAuthXC、Cookie-ERInfo（适合一些对隐私要求较高的场景）
 *                  - sign_cookie: 优先认证签名，签名过期候补认证cookie，Cookie-FSAuthXC、Cookie-ERInfo（适合URL下发后触发具有延迟性的场景）
 * @param path 文件path 支持 N|C|A|G|TN|TC|TA 类型
 * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天
 * @param extension 文件扩展名 不需要带点号（注: SDK会自动处理extensions可能为空的情况，默认为bin）
 * @return 下载链接
 */
String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, 
    String upstreamOwnerId, AuthModel authModel, String path, String signature, 
    long expireTime, String filename, String extension) throws StoneCommonClientException;
```

### 5.3 生成访问URL（无元数据签名）

```java
/**
 * 生成匿名访问的下载链接
 * 作用: 用以在业务给前端返回文件访问链接
 * @param fileTenantId 文件所属租户ID
 * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天
 * @param business 业务线标识（注：请勿使用特殊字符，长度不超过64个字符）
 * @return 下载链接
 */
String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, 
    String upstreamOwnerId, String path, String fileTenantId, String filename, 
    String extension, long expireTime, String business) throws StoneCommonClientException;
```

### 5.4 生成CDN访问URL（C|TC类型文件）

```java
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;

/**
 * 生成CDN加速的访问链接（仅支持C|TC类型文件）
 * 说明: 无需鉴权、没有有效期、明文参数
 * @param tenantId 企业ID，发起访问请求的企业ID 不能为空
 * @param path 文件path 支持 C|TC 类型
 * @param signature 元数据签名
 * @param extension 文件扩展名 不需要带.号
 * @param business 业务线标识（注：请勿使用特殊字符，长度不超过64个字符）
 * @return 下载链接
 * @throws StoneCommonClientException 生成CDN加速访问链接失败时抛出异常
 */
String generateCFileAccessUrl(String tenantId, String userId, String path, 
    String signature, String extension, String business) throws StoneCommonClientException;
```

**生成示例：**
```http
https://ceshi112.fspage.com/ImagesOne/?Cid=C_202310_25_ed772f591f724534947c75f7d1fb3b45&Acid=78437.78437.1000&Ext=jpg&Bn=test&Ds=Xio6cdf0-s1vYZHblHD6cw==
```

### 5.5 签名加解密

#### 签名加密

```java
/**
 * 签名加密方法（注：不建议缓存加密签名，签名每次加密后都不同）
 * @param said 言（一般是tenantId）
 * @param signature 文件元数据签名
 * @return 加密后的字符串，格式为Enc(加密内容)
 * 注：已加密的签名不会再次加密将原样返回，非法参数返回Optional.empty()
 */
Optional<String> encryptSignature(String said, String signature) throws StoneCommonClientException;
```

#### 签名解密

```java
/**
 * 加密签名解密方法
 * @param said 用于验证的言（一般是tenantId）
 * @param encCiphertext 加密的签名
 * @return 返回解密后的原始signature
 * 注：非加密的签名会被原样返回，非法参数返回Optional.empty()
 */
Optional<String> decryptSignature(String said, String encCiphertext) throws StoneCommonClientException;
```

---

## 6. 客户端使用指南

### 6.1 设计理念

**目的**：前端关注自身页面的展示与行为，不再关注server的业务逻辑与架构设计，让文件回归资源本身。

**前端不需要关心**：
- 使用什么URL展示图片、什么URL下载文件、什么URL预览文件
- 什么N、TN、A、TA、G、S、F等各种五花八门的文件类型
- 用户的自定义域名
- 上下游、无租户、1+N、企微、飞书连接器等等乱七八糟的逻辑

**前端需要关心**：
- 图片展示多大
- 文件是展示还是下载还是预览

### 6.2 URL使用模板

```ini
${Signature URL}?acModel=${inline|attachment|preview}&size=${w*h}&traceId=${custom traceId}&linkId=${business linkId}
```

### 6.3 参数说明

#### 6.3.1 Signature URL

**说明**：
- 一个包含了用户身份签名与文件信息说明的加密URL，使用该URL即可获取到文件
- 该URL具有有效期，目前server默认下发的过期时间为3600秒，即URL链接中的Ets参数
- **重要**：生成的URL本身一个字符都不可以改变（包括为URL进行编码），改变将无法使用

**URL示例**：（有效期至2025年11月27日，供测试使用）
```ini
https://img.ceshi112.com/FilesOne/?Fid=1F5B6CBDE22C3CCB67299FEFEF626117533572A666E9FC23995D3C65C647A31AE942F838ADA47813F037E744F8C5C52D78C8D8CF2FF71E65B3465F2BE681F8CB2DCAF314AA9A60F7A8AFB4D1DA53C1BC730A1C6A5446F7DF63839F1A8BD6BBBF84EBD2B9BF7813BCB5BED31CE26AD4C1204C2759E2491779&Acid=71554.1181&Ets=1764237446664&Ak=E66bW2HwtMREywR6Jkl7Jlpr&Fn=test.jpg&Sig=Tw1cx8Rb3VCJxMSfsBxKfgwvnN4=&Ds=xTWoDW_d4jC_kDbV8Ukwzw==
```

#### 6.3.2 acModel（访问模式）

**说明**：请求模型，用以控制文件请求的响应类型，参数不同Content-Disposition与Content-type响应头不同

| 值 | 说明 | 适用场景 |
|---|------|----------|
| **inline** | 浏览器直接打开（默认值） | 图片展示、文档预览 |
| **attachment** | 以附件形式下载 | 文件下载 |
| **preview** | 跳转到文件预览服务打开 | 文档预览（开发中，预期945可使用） |

**是否必须**：否

**支持的文件类型**：
- **图片类型**：png、webp、jpeg、jpg、bmp、gif...
- **纯文本**：txt、sql、js、css、csv、json、md、xml、py、java...
- **多媒体**：mp4、mp3...

**使用示例**：

inline模式：
```ini
https://img.ceshi112.com/FilesOne/?Fid=...&acModel=inline
```

attachment模式：
```ini
https://img.ceshi112.com/FilesOne/?Fid=...&acModel=attachment
```

**注意**：考虑到下载与展示不同，下载相对于展示实际发起请求可能存在滞后，因此附件下发的${Signature URL}在有效期过期后兜底校验Cookie（FSAuthX、ERInfo）

#### 6.3.3 size（图片尺寸）

**说明**：用以控制响应图片大小

| 值 | 说明 |
|---|------|
| **0*0** | 请求原图（默认值） |
| **400*400** | 需要服务端返回宽400X高400分辨率大小的图片 |
| **400*0** | 需要服务端返回等比400分辨率大小，即宽高均为400的图片 |

**限制**：
- 最大阈值：width <= 5000, height <= 3000，超出将默认返回原图
- **仅适用于图片**：仅支持jpg、jpeg、png、webp、bmp、gif类型
- **强烈建议**：根据业务场景需要调整size大小

**是否必须**：否

**使用示例**：

请求原图：
```ini
https://img.ceshi112.com/FilesOne/?Fid=...
# 等效于
https://img.ceshi112.com/FilesOne/?Fid=...&size=0*0
```

请求宽400高400分辨率：
```ini
https://img.ceshi112.com/FilesOne/?Fid=...&size=400*400
```

#### 6.3.4 traceId（追踪ID）

**说明**：终端调用时传入，便于问题排查、请求追踪

**是否必须**：否

**重要提示**：极为建议传递该参数，缺少该参数排查问题将会极为困难

#### 6.3.5 linkId（业务关联ID）

**说明**：业务Server生成signedUrl时追加在signedUrl后，方便追踪具体业务，便于排查问题

**是否必须**：否

**重要提示**：极为建议传递该参数，缺少该参数排查问题将会极为困难

---

## 7. 上传功能详解

### 7.1 获取签名上传信息

```java
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.UploadFileOmit;

/**
 * 获取签名及上传接口信息（不支持跨云，当前测试阶段可重放，后期将禁止签名重放）
 * @param tenantId 企业ID，发起访问请求的企业ID，不能为空
 * @param expireTime 上传签名从当前时间起有效时间 最小为60秒 最大为604800秒即7天
 * @param resourceType 文件资源类型，不能为空；如N、TN、C、TC
 * 注: C|TC 类型为无隐私CDN加速文件，要求文件类型必须为图片，且仅支持JPG、JPEG、PNG、GIF、BMP、WEBP类型
 *     如果文件实际类型为其他类型，请使用N|TN类型，否则即使指定为C|TC类型，上传时也会被转换为N|TN类型
 *     T开头的临时文件类型默认有效期3天，正式文件默认永久有效
 * @param isStreamUpload 是否为流式上传，默认为false
 * @param filename 完整文件名 即携带扩展名
 * 注: SDK会自动对文件名进行URL编码，调用方无需关注
 *    SDK会自动处理filename可能为空的情况，默认为YYYY-MM-DD-HH+扩展名
 *    如果文件名包含不支持URL编码的字符同样使用默认文件名
 * @param extension 文件扩展名 不需要带点号（注: SDK会自动处理extensions可能为空的情况，默认为bin）
 * @param fileSize 文件大小 单位byte 不能为空 取值范围[1,104857600]，大文件上传请使用分片上传
 * @return 签名及上传接口信息 {@link UploadFileOmit}
 * @throws StoneCommonClientException 上传信息生成失败时抛出异常
 */
UploadFileOmit generateUploadFileOmit(String tenantId, long expireTime, 
    FileResourceEnum resourceType, boolean isStreamUpload, String filename, 
    String extension, int fileSize) throws StoneCommonClientException;
```

### 7.2 响应数据示例

```json
{
    "method": "POST",
    "url": "https://img.ceshi112.com/FilesOne/",
    "acid": "71554.-10000",
    "resource": "TC",
    "ak": "8ko2UvJ2ScCiPm57W1lvGuGu",
    "sign": "iq1kQ2aP36NfDJVn9MLV11XIUxY=",
    "expiry": 1712074007625,
    "filename": "%E7%BA%B7%E4%BA%AB%E5%B0%8F%E8%9C%9C%E8%9C%82.webp",
    "size": 45047,
    "digest": "hh_8bCLjBdA67SYwmW0HIA==",
    "contentType": "multipart/form-data"
}
```

**注意**：除method和url外，其余均为请求头参数。contentType需要特殊处理。

### 7.3 上传方式详解

#### 7.3.1 跨域说明

**重要提示**：
- 该接口允许跨域上传，并且允许来自任意域名的请求
- 客户端不能以`withCredentials=true`的形式请求，如果以`withCredentials=true`的形式请求跨域检测将不能通过
- 跨域只存在于浏览器端，不存在于android/ios/Node.js/python/java/微信小程序等环境
- 微信小程序实际由微信服务端发起请求所以规避了跨域

#### 7.3.2 表单上传

**cURL示例**：
```bash
# 请注意不要去掉请求时URL路径后的 /，该请求为严格匹配模式，请求路径必须为 /FilesOne/
# 建议添加traceId 便于排查和追踪问题
curl --request POST 'https://img.ceshi112.com/FilesOne/?traceId=E-E.71554.1000-10882367' \
--header 'acid: 71554.-10000' \
--header 'resource: TC' \
--header 'ak: 8ko2UvJ2ScCiPm57W1lvGuGu' \
--header 'sign: iq1kQ2aP36NfDJVn9MLV11XIUxY' \
--header 'expiry: 1712074007625' \
--header 'filename: "%E7%BA%B7%E4%BA%AB%E5%B0%8F%E8%9C%9C%E8%9C%82.webp"' \
--header 'size: 45047' \
--header 'digest: hh_8bCLjBdA67SYwmW0HIA==' \
--header 'Content-Type: multipart/form-data' \
--form 'facishareFile=@/your-file-path/your-file-name.webp'
```

**注意事项**：
- 文件需以表单形式上传
- 表单项属性名必须为`facishareFile`
- 一次仅接收一个文件（其他表单项均不会处理）
- 表单上传浏览器或API一般会默认指定Content-Type为multipart/form-data，但还是建议手动传递防止浏览器或API行为变更影响业务（微信小程序除外）

#### 7.3.3 流式上传

**cURL示例**：
```bash
curl --request POST 'https://img.ceshi112.com/FilesOne/?traceId=E-E.71554.1000-10882367' \
--header 'acid: 71554.-10000' \
--header 'resource: TC' \
--header 'ak: 8ko2UvJ2ScCiPm57W1lvGuGu' \
--header 'sign: iq1kQ2aP36NfDJVn9MLV11XIUxY' \
--header 'expiry: 1712074007625' \
--header 'filename: "%E7%BA%B7%E4%BA%AB%E5%B0%8F%E8%9C%9C%E8%9C%82.webp"' \
--header 'size: 45047' \
--header 'digest: hh_8bCLjBdA67SYwmW0HIA==' \
--header 'Content-Type: application/octet-stream' \
--data-binary '@/your-file-path/your-file-name.webp'
```

**注意事项**：
- 流式上传时，一定要显示指定Content-Type为`application/octet-stream`
- 否则浏览器或API可能自动计算类型，导致上传失败
- 文件需以二进制流的形式上传

#### 7.3.4 网页上传示例

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Example</title>
</head>
<body>
    <h1>File Upload Example</h1>
    <form id="uploadForm">
        <input type="file" id="fileInput" name="file">
        <button type="submit">Upload</button>
    </form>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(event) {
            event.preventDefault();
            var fileInput = document.getElementById('fileInput');
            
            if (fileInput.files.length === 0) {
                alert('Please select a file.');
                return;
            }
            
            var myHeaders = new Headers();
            myHeaders.append("acid", "71554.-10000");
            myHeaders.append("resource", "TC");
            myHeaders.append("ak", "8ko2UvJ2ScCiPm57W1lvGuGu");
            myHeaders.append("sign", "kEbEH7yDjkgxEqz1jTOfMwifjNs=");
            myHeaders.append("expiry", "1715937477938");
            myHeaders.append("filename", "%E7%BA%B7%E4%BA%AB%E5%B0%8F%E8%9C%9C%E8%9C%82.webp");
            myHeaders.append("size", "45047");
            myHeaders.append("digest", "kXwWIm49Bk8xreU8n_yW-g==");

            var formdata = new FormData();
            formdata.append("facishareFile", fileInput.files[0], "纷享小蜜蜂.webp");

            var requestOptions = {
                method: 'POST',
                headers: myHeaders,
                body: formdata,
                redirect: 'follow'
            };

            fetch("https://img.ceshi112.com/FilesOne/", requestOptions)
                .then(response => response.text())
                .then(result => console.log(result))
                .catch(error => console.log('error', error));
        });
    </script>
</body>
</html>
```

#### 7.3.5 微信小程序上传

```javascript
wx.uploadFile({
    url: 'https://img.ceshi112.com/FilesOne/?traceId=E-E.71554.1000-10882367',
    filePath: tempFilePaths[0],
    name: "facishareFile",
    header: {
        "acid": "71554.-10000",
        "resource": "TC",
        "ak": "8ko2UvJ2ScCiPm57W1lvGuGu",
        "sign": "iq1kQ2aP36NfDJVn9MLV11XIUxY=",
        "expiry": "1712074007625",
        "filename": "%E7%BA%B7%E4%BA%AB%E5%B0%8F%E8%9C%9C%E8%9C%82.webp",
        "size": "45047",
        "digest": "hh_8bCLjBdA67SYwmW0HIA==",
    },
    success(res) {
        const data = res.data
        this.setData({
            "userInfo.avatarUrl": data.url,
        })
    }
})
```

**注意事项**：
1. 地址后的斜杠`/`不可去除
2. 通过微信小程序上传不可显式指定Content-Type，仅传递上述示例Header头即可
3. 微信小程序显式指定会导致boundary丢失，参考链接：https://developers.weixin.qq.com/community/develop/doc/0004e8aef5cb4834b1ec992835b400

### 7.4 上传响应示例

#### 7.4.1 上传成功

```json
{
    "success": true,
    "code": 200,
    "message": "success",
    "data": "TN_7c1fabc747264f4a9e0ae7301430df18"
}
```

```json
{
    "success": true,
    "code": 200,
    "message": "success",
    "data": "TC_e0becae0d32b4a1eb0d9b78f54d556cb"
}
```

**验证上传结果**：
```http
https://ceshi112.fspage.com/image/71554/TC_e0becae0d32b4a1eb0d9b78f54d556cb
```

#### 7.4.2 上传失败

```json
{
    "success": false,
    "code": 400,
    "message": "签名已过期",
    "data": null
}
```

---

## 8. 常见问题

### 8.1 URL使用注意事项

1. **不可修改URL**：生成的签名URL一个字符都不能改变，包括URL编码
2. **有效期管理**：注意URL的有效期，过期后需要重新生成
3. **参数追加**：可以通过`?`或`&`追加acModel、size、traceId等参数

### 8.2 文件类型说明

| 类型 | 说明 | 特点 |
|------|------|------|
| **N** | 正式文件 | 永久有效 |
| **TN** | 临时文件 | 默认3天有效期 |
| **C** | CDN加速正式文件 | 无隐私要求、仅支持图片 |
| **TC** | CDN加速临时文件 | 无隐私要求、仅支持图片、默认3天有效期 |
| **A/TA** | 附件类型 | 对应正式/临时附件 |
| **G** | 通用类型 | 特殊场景使用 |

### 8.3 错误排查

1. **使用traceId**：强烈建议在所有请求中添加traceId参数
2. **使用linkId**：业务层面添加linkId便于问题追踪
3. **查看错误码**：根据返回的错误码和message进行问题定位

### 8.4 性能优化建议

1. **图片尺寸**：根据实际显示需要设置合适的size参数，避免请求原图
2. **缓存策略**：合理设置URL有效期，平衡安全性和用户体验
3. **CDN使用**：对于无隐私要求的图片文件，建议使用C/TC类型获得更好的访问性能

### 8.5 安全建议

1. **签名加密**：敏感场景下使用签名加解密功能
2. **认证模式**：根据业务场景选择合适的认证模式（sign/cookie/sign_cookie）
3. **有效期控制**：设置合理的URL有效期，不宜过长

---

## 联系方式

**技术咨询答疑**：@安宜龙Andy

---

*最后更新时间：2025-06-13*