package com.fxiaoke.stone.commons.impl;

import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;
import org.openjdk.jmh.annotations.Benchmark;
import org.openjdk.jmh.annotations.BenchmarkMode;
import org.openjdk.jmh.annotations.Fork;
import org.openjdk.jmh.annotations.Level;
import org.openjdk.jmh.annotations.Measurement;
import org.openjdk.jmh.annotations.Mode;
import org.openjdk.jmh.annotations.OutputTimeUnit;
import org.openjdk.jmh.annotations.Scope;
import org.openjdk.jmh.annotations.Setup;
import org.openjdk.jmh.annotations.State;
import org.openjdk.jmh.annotations.Warmup;

@State(Scope.Benchmark)
public class GenerateUrlBenchmark {

  private AppFrameworkExclusiveClient appFrameworkExclusiveClient= new AppFrameworkExclusiveClient(null);

  @Setup(Level.Trial)
  public void setup() {
    System.setProperty("spring.profiles.active", "fstest");
    // 设置 credentialsOmitCache 缓存
    appFrameworkExclusiveClient.credentialsOmitCache = CacheBuilder.newBuilder().maximumSize(5)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            CredentialsOmit credentialsOmit = new CredentialsOmit();
            credentialsOmit.setAccessKey("E66bW2HwtMREywR6Jkl7Jlpr");
            credentialsOmit.setSecretKey("805PK2jPfVP8w0cE5bOC7NUWEfp1Yi");
            return credentialsOmit;
          }
        });
    // 初始化 envEiMappingCache 缓存
    initEnvEiMappingCache();
  }

  @Benchmark
  @Warmup(iterations = 10, time = 5, timeUnit = TimeUnit.SECONDS)
  @Measurement(iterations = 10, time = 5, timeUnit = TimeUnit.SECONDS)
  @Fork(1)
  @BenchmarkMode(Mode.AverageTime)
  @OutputTimeUnit(TimeUnit.MILLISECONDS)
  public void testGenerateUrl() {
    appFrameworkExclusiveClient.generateUrl("71554", "1181", "", "", "",
        "N_202406_13_dbf22d0a67e84705b98c907200989c69",
        "71554$fJeOha_cKfJwnZrFF-gLlTL72gE=$E66bW2HwtMREywR6Jkl7Jlpr", 3600L, "test.jpg",
        "jpg");
  }


  public static void main(String[] args) throws Exception {
    org.openjdk.jmh.Main.main(args);
  }

  private void initEnvEiMappingCache() {
    appFrameworkExclusiveClient.envEiMappingCache = CacheBuilder.newBuilder().maximumSize(200).build();
    EnvEIMapping envEIMapping = new EnvEIMapping();
    envEIMapping.setCdnDomain("ceshi112.fspage.com");
    envEIMapping.setHomeDomain("crm.ceshi112.com");
    envEIMapping.setDomain("img.ceshi112.com");
    envEIMapping.setProtocol("https");
    envEIMapping.setContextPath("FilesOne/");
    envEIMapping.setCdnContextPath("ImagesOne/");
    envEIMapping.setAccessKey("nPRQNw1g2YB0ar15XaKDGq3s");
    envEIMapping.setSecretKey("WEjagM4XghgtqbNR90NJdEKrfjQZjz");
    appFrameworkExclusiveClient.envEiMappingCache.put("fstest", envEIMapping);
  }
}
