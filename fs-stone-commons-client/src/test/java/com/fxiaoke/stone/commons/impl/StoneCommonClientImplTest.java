package com.fxiaoke.stone.commons.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fxiaoke.stone.commons.domain.api.GeneratorSignDocumentPreviewUrl;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignDownloadUrl;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignPreviewUrl;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.model.AcUser;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.fxiaoke.stone.commons.domain.model.FileInfo;
import com.fxiaoke.stone.commons.domain.model.FileUser;
import com.fxiaoke.stone.commons.domain.model.RequestTemplate;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.lang.reflect.Field;
import javax.annotation.Nonnull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class StoneCommonClientImplTest {

  @BeforeAll
  public static void setProfile(){
    // 设置系统属性
    System.setProperty("spring.profiles.active", "fstest");
  }

  @Test
  void generatorDownloadUrl() throws NoSuchFieldException, IllegalAccessException {
    // 创建 StoneCommonClient 对象
    StoneCommonClientImpl stoneCommonClient = getStoneCommonClient();
    stoneCommonClient.credentialsOmitCache = getCredentialsOmitCache();
    stoneCommonClient.envEiMappingCache = getEnvEiMappingCache();
    GeneratorSignDownloadUrl.Arg arg = new GeneratorSignDownloadUrl.Arg();
    AcUser acUser = new AcUser();
    acUser.setTenantId(71554);
    acUser.setUserId(1181);
    arg.setAcUser(acUser);
    FileUser fileUser = new FileUser();
    fileUser.setTenantId(71554);
    arg.setFileUser(fileUser);
    arg.setFileUser(fileUser);
    FileInfo fileInfo = new FileInfo();
    fileInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fileInfo.setFilename("test.jpg");
    fileInfo.setExtension("jpg");
    arg.setFileInfo(fileInfo);
    arg.setExpireTime(3600L);
    arg.setBusiness("openApi");
    GeneratorSignDownloadUrl.Result result = stoneCommonClient.generatorDownloadUrl(arg);
    System.out.println(result);
    assertNotNull(result);
  }

  @Test
  void generatorPreviewUrl() throws NoSuchFieldException, IllegalAccessException {
    // 创建 StoneCommonClient 对象
    StoneCommonClientImpl stoneCommonClient=getStoneCommonClient();
    stoneCommonClient.credentialsOmitCache=getCredentialsOmitCache();
    stoneCommonClient.envEiMappingCache=getEnvEiMappingCache();
    GeneratorSignPreviewUrl.Arg arg =new GeneratorSignPreviewUrl.Arg();
    AcUser acUser=new AcUser();
    acUser.setTenantId(71554);
    acUser.setUserId(1181);
    arg.setAcUser(acUser);
    FileUser fileUser=new FileUser();
    fileUser.setTenantId(71554);
    arg.setFileUser(fileUser);
    arg.setFileUser(fileUser);
    FileInfo fileInfo=new FileInfo();
    fileInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fileInfo.setFilename("test.jpg");
    fileInfo.setExtension("jpg");
    arg.setFileInfo(fileInfo);
    arg.setExpireTime(3600L);
    arg.setBusiness("openApi");
    GeneratorSignPreviewUrl.Result result=stoneCommonClient.generatorPreviewUrl(arg);
    System.out.println(result);
    assertNotNull(result);
  }

  @Test
  void generatorDocumentPreviewUrl() throws NoSuchFieldException, IllegalAccessException {
    // 创建 StoneCommonClient 对象
    StoneCommonClientImpl stoneCommonClient=getStoneCommonClient();
    stoneCommonClient.credentialsOmitCache=getCredentialsOmitCache();
    stoneCommonClient.envEiMappingCache=getEnvEiMappingCache();
    GeneratorSignDocumentPreviewUrl.Arg arg =new GeneratorSignDocumentPreviewUrl.Arg();
    AcUser acUser=new AcUser();
    acUser.setTenantId(71554);
    acUser.setUserId(1181);
    arg.setAcUser(acUser);
    FileUser fileUser=new FileUser();
    fileUser.setTenantId(71554);
    arg.setFileUser(fileUser);
    arg.setFileUser(fileUser);
    FileInfo fileInfo=new FileInfo();
    fileInfo.setPath("N_202410_23_9595b6dd7e9048a1b304d370fbf19c0a");
    fileInfo.setFilename("test.pdf");
    fileInfo.setExtension("pdf");
    arg.setFileInfo(fileInfo);
    arg.setExpireTime(3600L);
    arg.setBusiness("openApi");
    GeneratorSignDocumentPreviewUrl.Result result=stoneCommonClient.generatorDocumentPreviewUrl(arg);
    System.out.println(result);
    assertNotNull(result);
  }


  private static LoadingCache<Long, CredentialsOmit> getCredentialsOmitCache(){
    return CacheBuilder.newBuilder().maximumSize(5)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            CredentialsOmit credentialsOmit = new CredentialsOmit();
            credentialsOmit.setAccessKey("E66bW2HwtMREywR6Jkl7Jlpr");
            credentialsOmit.setSecretKey("805PK2jPfVP8w0cE5bOC7NUWEfp1Yi");
            return credentialsOmit;
          }
        });
  }

  private static Cache<String, EnvEIMapping> getEnvEiMappingCache() {
    // 创建并设置 EnvEIMapping 对象
    EnvEIMapping envEIMapping = new EnvEIMapping();
    envEIMapping.setCdnDomain("ceshi112.fspage.com");
    envEIMapping.setHomeDomain("crm.ceshi112.com");
    envEIMapping.setDomain("img.ceshi112.com");
    envEIMapping.setProtocol("https");
    envEIMapping.setContextPath("FilesOne/");
    envEIMapping.setCdnContextPath("ImagesOne/");
    envEIMapping.setAccessKey("nPRQNw1g2YB0ar15XaKDGq3s");
    envEIMapping.setSecretKey("WEjagM4XghgtqbNR90NJdEKrfjQZjz");
    // 创建并设置缓存
    Cache<String, EnvEIMapping> envEiMappingCache = CacheBuilder.newBuilder().maximumSize(200).build();
    envEiMappingCache.put("fstest", envEIMapping);
    return envEiMappingCache;
  }

  private static StoneCommonClientImpl getStoneCommonClient() throws NoSuchFieldException, IllegalAccessException {
    StoneCommonClientImpl stoneCommonClient = new StoneCommonClientImpl(null, BusinessEnum.OPEN_API);

    // 获取 StoneCommonClientImpl 类
    Class<? extends StoneCommonClientImpl> stoneCommonClientClientClass = stoneCommonClient.getClass();

    RequestTemplate requestTemplate=new RequestTemplate();
    requestTemplate.setAnonymitySignRequestTemplate("%s://%s/%sAnonymity/Sign?Fid=%s&Acid=%s&Ets=%d&Ak=%s&Fn=%s&Bn=%s&Sig=%s&Ds=%s");
    requestTemplate.setAnonymityDocumentPreviewRequestTemplate("%s://%s/dps/preview/bysharetoken?shareToken=%s&acid=%s");
    // 使用反射设置私有字段 requestTemplate
    Field requestTemplateField = stoneCommonClientClientClass.getDeclaredField("requestTemplate");
    requestTemplateField.setAccessible(true);
    requestTemplateField.set(stoneCommonClient, requestTemplate);

    return stoneCommonClient;
  }
}