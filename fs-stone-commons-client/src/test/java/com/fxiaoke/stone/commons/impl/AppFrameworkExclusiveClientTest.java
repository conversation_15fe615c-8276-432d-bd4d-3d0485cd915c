package com.fxiaoke.stone.commons.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import javax.annotation.Nonnull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class AppFrameworkExclusiveClientTest {

  private static AppFrameworkExclusiveClient appFrameworkExclusiveClient;

  @BeforeAll
  public static void setProfile() {
    // 设置系统属性
    System.setProperty("spring.profiles.active", "fstest");
    appFrameworkExclusiveClient = new AppFrameworkExclusiveClient(null);
    appFrameworkExclusiveClient.credentialsOmitCache=getCredentialsOmitCache();
    appFrameworkExclusiveClient.envEiMappingCache=getEnvEiMappingCache();
  }

  private static final String DEFAULT_TENANT_ID ="71554";
  private static final String DEFAULT_USER_ID ="1181";
  private static final String DEFAULT_EXT = "jpg";
  private static final String DEFAULT_FILENAME = "test.jpg";
  private static final long DEFAULT_EXPIRE_TIME = 3600L;
  
  private static final String DEFAULT_BUSINESS ="test";
  private static final String DEFAULT_N_PATH = "N_202406_13_dbf22d0a67e84705b98c907200989c69";
  private static final String DEFAULT_N_PATH_SIGN = "71554$fJeOha_cKfJwnZrFF-gLlTL72gE=$E66bW2HwtMREywR6Jkl7Jlpr";
  private static final String DEFAULT_N_PATH_ENCRY_SIGN = "Enc(E96702129194D0FF442B47D60713D2FCA0F3EA3F6B1338B039C7FEFFD54C90E5F741F4FDE6D4ABBCCF638E3D848CD658FB4F075EFB5E01D2942077E9C8BD5605C372582772569736377B484554D8EBB0)";
  private static final String DEFAULT_C_PATH = "C_202404_23_e513dbd479ef4a03a79463e5ff9ef978";
  private static final String DEFAULT_C_PATH_SIGN = "71554$LGigcJKqgMEaJP1Ze0gOpuzJx0g=$E66bW2HwtMREywR6Jkl7Jlpr";
  
  @Test
  void generateCFileAccessUrl()  {
    // 调用 generateCFileAccessUrl 方法
    String cFileAccessUrl = appFrameworkExclusiveClient.generateCFileAccessUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID,
        DEFAULT_C_PATH, DEFAULT_C_PATH_SIGN, DEFAULT_EXT, "app_framework");
    // 验证生成的URL是否符合预期
    System.out.println(cFileAccessUrl);
    assertNotNull(cFileAccessUrl);
  }

  @Test
  void generateUrlTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID, "", "", "",
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void encryptSignatureTest(){
    String encryptSignature = appFrameworkExclusiveClient.encryptSignature(DEFAULT_TENANT_ID, DEFAULT_N_PATH_SIGN).orElse(null);
    assertNotNull(encryptSignature);
  }

  @Test
  void decryptSignatureTest(){
    String decryptSignature = appFrameworkExclusiveClient.decryptSignature(DEFAULT_TENANT_ID, DEFAULT_N_PATH_ENCRY_SIGN).orElse(null);
    assertEquals(DEFAULT_N_PATH_SIGN, decryptSignature);
  }

  @Test
  void generateUrlByAuthModelSignTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID, "", "", "",
        AuthModel.SIGN,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateUrlByAuthModelSignAndEncrySignTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID, "", "", "",
        AuthModel.SIGN,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_ENCRY_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateUrlByAuthModelSignCookieAuthXCTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID, "", "", "",
        AuthModel.SIGN_COOKIE,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateUrlByAuthModelSignCookieEm6Test()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl("74164", "", "300110082", "300392433", "",
        AuthModel.SIGN_COOKIE,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateUrlByAuthModelCookieAuthXCTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID, "", "", "",
        AuthModel.COOKIE,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateUrlByAuthModelCookieEm6Test()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl("74164", "", "300110082", "300392433", "",
        AuthModel.COOKIE,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateUrlByAuthModelCookieTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl("74164", "", "300110082", "300392433", "",
        AuthModel.COOKIE_ALL,
        DEFAULT_N_PATH,
        DEFAULT_N_PATH_SIGN, DEFAULT_EXPIRE_TIME,
        DEFAULT_FILENAME, DEFAULT_EXT);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  @Test
  void generateNotSignUrlTest()  {
    String signAccessUrl = appFrameworkExclusiveClient.generateUrl(DEFAULT_TENANT_ID,
        DEFAULT_USER_ID, "", "", "",
        DEFAULT_N_PATH, DEFAULT_TENANT_ID, DEFAULT_FILENAME, DEFAULT_EXT, DEFAULT_EXPIRE_TIME,
        DEFAULT_BUSINESS);
    System.out.println(signAccessUrl);
    assertNotNull(signAccessUrl);
  }

  private static LoadingCache<Long, CredentialsOmit> getCredentialsOmitCache(){
    return CacheBuilder.newBuilder().maximumSize(5)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            CredentialsOmit credentialsOmit = new CredentialsOmit();
            credentialsOmit.setAccessKey("WH07ZwPp6ng2sXy7uwthKJz3");
            credentialsOmit.setSecretKey("CddM1ML7COaL9WW6pTopvBEA9cEsgO");
            return credentialsOmit;
          }
        });
  }

  private static Cache<String, EnvEIMapping> getEnvEiMappingCache() {
    // 创建并设置 EnvEIMapping 对象
    EnvEIMapping envEIMapping = new EnvEIMapping();
    envEIMapping.setCdnDomain("ceshi112.fspage.com");
    envEIMapping.setHomeDomain("crm.ceshi112.com");
    envEIMapping.setDomain("img.ceshi112.com");
    envEIMapping.setProtocol("https");
    envEIMapping.setContextPath("FilesOne/");
    envEIMapping.setCdnContextPath("ImagesOne/");
    envEIMapping.setAccessKey("nPRQNw1g2YB0ar15XaKDGq3s");
    envEIMapping.setSecretKey("WEjagM4XghgtqbNR90NJdEKrfjQZjz");
    // 创建并设置缓存
    Cache<String, EnvEIMapping> envEiMappingCache = CacheBuilder.newBuilder().maximumSize(200).build();
    envEiMappingCache.put("fstest", envEIMapping);
    return envEiMappingCache;
  }
}