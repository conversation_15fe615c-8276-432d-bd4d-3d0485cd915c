package com.fxiaoke.stone.commons.impl;

import org.openjdk.jmh.annotations.*;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;

@State(Scope.Benchmark)
public class SignatureBenchmark {

  private MetadataExclusiveClient metadataExclusiveClient;

  @Setup(Level.Trial)
  public void setup() {
    metadataExclusiveClient = new MetadataExclusiveClient(null);
    metadataExclusiveClient.credentialsOmitCache = CacheBuilder.newBuilder().maximumSize(5)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            CredentialsOmit credentialsOmit = new CredentialsOmit();
            credentialsOmit.setAccessKey("E66bW2HwtMREywR6Jkl7Jlpr");
            credentialsOmit.setSecretKey("805PK2jPfVP8w0cE5bOC7NUWEfp1Yi");
            return credentialsOmit;
          }
        });
  }

  @Benchmark
  @Warmup(iterations = 10, time = 5, timeUnit = TimeUnit.SECONDS)
  @Measurement(iterations = 10, time = 5, timeUnit = TimeUnit.SECONDS)
  @Fork(1)
  @BenchmarkMode(Mode.AverageTime)
  @OutputTimeUnit(TimeUnit.MILLISECONDS)
  public void testSignature() {
    metadataExclusiveClient.signature(71554, "N_202406_13_dbf22d0a67e84705b98c907200989c69");
  }

  public static void main(String[] args) throws Exception {
    org.openjdk.jmh.Main.main(args);
  }
}