package com.fxiaoke.stone.commons.impl;

import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import java.util.Optional;
import javax.annotation.Nonnull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class MetadataExclusiveClientTest {

  private static final String SAID = "90071";
  private static final long DEFAULT_TENANT_ID =71554;
  private static final String DEFAULT_N_PATH = "N_202406_13_dbf22d0a67e84705b98c907200989c69";
  private static final String DEFAULT_N_PATH_SIGN = "71554$fJeOha_cKfJwnZrFF-gLlTL72gE=$E66bW2HwtMREywR6Jkl7Jlpr";
  private static final String DEFAULT_ENC_SIGN="Enc(961727D3BB1F2A6F6911BE9D120F432F342A0018BF0A938FE49D6B10DF471E84D519A337845C754ED3ECC14EF0BB9166B68F431F93C4D93DEB8CC69CCCC0DEBB12DD6AFB3473712F12B2EF7F25C71AE5)";

  private static MetadataExclusiveClient metadataExclusiveClient;

  @BeforeAll
  public static void setProfile() {
    // 设置系统属性
    System.setProperty("spring.profiles.active", "fstest");
    metadataExclusiveClient = new MetadataExclusiveClient(null);
    metadataExclusiveClient.credentialsOmitCache = CacheBuilder.newBuilder().maximumSize(5)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            CredentialsOmit credentialsOmit = new CredentialsOmit();
            credentialsOmit.setAccessKey("E66bW2HwtMREywR6Jkl7Jlpr");
            credentialsOmit.setSecretKey("805PK2jPfVP8w0cE5bOC7NUWEfp1Yi");
            return credentialsOmit;
          }
        });
  }

  @Test
  void signature() {
    String signature = metadataExclusiveClient.signature(DEFAULT_TENANT_ID,DEFAULT_N_PATH);
    assert signature.equals(DEFAULT_N_PATH_SIGN);
  }

  @Test
  void generateSignature(){
    Optional<String> stringOptional = metadataExclusiveClient.generateSignature(DEFAULT_TENANT_ID, DEFAULT_N_PATH);
    if (stringOptional.isPresent()) {
      String signature = stringOptional.get();
      assert signature.equals(DEFAULT_N_PATH_SIGN);
    }
  }

  @Test
  void testEncryptSignature() {
    // 正常情况
    Optional<String> result = metadataExclusiveClient.encryptSignature(SAID, DEFAULT_N_PATH_SIGN);
    System.out.println(result);
    Assertions.assertTrue(result.isPresent());

    // 空 said
    result = metadataExclusiveClient.encryptSignature("", DEFAULT_N_PATH_SIGN);
    System.out.println(result);
    Assertions.assertFalse(result.isPresent());

    // 空 signature
    result = metadataExclusiveClient.encryptSignature(SAID, "");
    System.out.println(result);
    Assertions.assertFalse(result.isPresent());

    // 已加密的 signature
    result = metadataExclusiveClient.encryptSignature(SAID, DEFAULT_ENC_SIGN);
    Assertions.assertTrue(result.isPresent());
    System.out.println(result);
    Assertions.assertEquals(DEFAULT_ENC_SIGN, result.get());
  }

  @Test
  void testDecryptSignature() {

    // 测试空参数情况
    Optional<String> result = metadataExclusiveClient.decryptSignature(null, "");
    Assertions.assertFalse(result.isPresent());

    result = metadataExclusiveClient.decryptSignature("", "");
    Assertions.assertFalse(result.isPresent());

    result = metadataExclusiveClient.decryptSignature(SAID, null);
    Assertions.assertFalse(result.isPresent());

    result = metadataExclusiveClient.decryptSignature(SAID, "");
    Assertions.assertFalse(result.isPresent());

    // 测试非标准加密签名与原始签名要求解密的情况
    result = metadataExclusiveClient.decryptSignature(SAID, DEFAULT_N_PATH_SIGN);
    Assertions.assertEquals(Optional.of(DEFAULT_N_PATH_SIGN), result);

    // 测试不匹配的 said
    result = metadataExclusiveClient.decryptSignature("71554", DEFAULT_ENC_SIGN);
    Assertions.assertEquals(Optional.empty(), result);

    // 测试加密签名的正常情况
    result = metadataExclusiveClient.decryptSignature(SAID, DEFAULT_ENC_SIGN);
    Assertions.assertEquals(Optional.of(DEFAULT_N_PATH_SIGN), result);
  }

  @Test
  void testSignaturePerformance() {
    long startTime = System.nanoTime();
    for (int i = 0; i < 1000000; i++) {
      String signature = metadataExclusiveClient.signature(DEFAULT_TENANT_ID, DEFAULT_N_PATH);
      // Optionally verify the signature if needed
      Assertions.assertEquals(DEFAULT_N_PATH_SIGN, signature);
    }
    long endTime = System.nanoTime();
    long duration = (endTime - startTime) / 1_000_000; // Convert to milliseconds
    System.out.println("Total time for 1,000,000 iterations: " + duration + " ms");
  }
}