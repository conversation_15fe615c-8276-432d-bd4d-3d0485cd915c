package com.fxiaoke.stone.commons.impl;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.fxiaoke.stone.commons.domain.model.UploadFileOmit;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.lang.reflect.Field;
import javax.annotation.Nonnull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class MarketingExclusiveClientTest {

  @BeforeAll
  public static void setProfile(){
    // 设置系统属性
    System.setProperty("spring.profiles.active", "fstest");
  }

  @Test
  void generateUploadFileOmit() throws NoSuchFieldException, IllegalAccessException {
    MarketingExclusiveClient marketingExclusiveClient = getMarketingExclusiveClient();
    marketingExclusiveClient.credentialsOmitCache=getCredentialsOmitCache();
    marketingExclusiveClient.envEiMappingCache=getEnvEiMappingCache();
    UploadFileOmit signatureOmit = marketingExclusiveClient.generateUploadFileOmit("71554",
        604800L, FileResourceEnum.TN, true, "test.jpg", "jpg", 119991);
    System.out.println(JSON.toJSON(signatureOmit));
  }

  private static LoadingCache<Long, CredentialsOmit> getCredentialsOmitCache(){
    return CacheBuilder.newBuilder().maximumSize(5)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            CredentialsOmit credentialsOmit = new CredentialsOmit();
            credentialsOmit.setAccessKey("E66bW2HwtMREywR6Jkl7Jlpr");
            credentialsOmit.setSecretKey("805PK2jPfVP8w0cE5bOC7NUWEfp1Yi");
            return credentialsOmit;
          }
        });
  }

  private static Cache<String, EnvEIMapping> getEnvEiMappingCache() {
    // 创建并设置 EnvEIMapping 对象
    EnvEIMapping envEIMapping = new EnvEIMapping();
    envEIMapping.setCdnDomain("ceshi112.fspage.com");
    envEIMapping.setHomeDomain("crm.ceshi112.com");
    envEIMapping.setDomain("img.ceshi112.com");
    envEIMapping.setProtocol("https");
    envEIMapping.setContextPath("FilesOne/");
    envEIMapping.setCdnContextPath("ImagesOne/");
    envEIMapping.setAccessKey("nPRQNw1g2YB0ar15XaKDGq3s");
    envEIMapping.setSecretKey("WEjagM4XghgtqbNR90NJdEKrfjQZjz");
    // 创建并设置缓存
    Cache<String, EnvEIMapping> envEiMappingCache = CacheBuilder.newBuilder().maximumSize(200).build();
    envEiMappingCache.put("fstest", envEIMapping);
    return envEiMappingCache;
  }

  private static MarketingExclusiveClient getMarketingExclusiveClient() throws NoSuchFieldException, IllegalAccessException {
    MarketingExclusiveClient marketingExclusiveClient = new MarketingExclusiveClient(null);
    // 获取 marketingExclusiveClient 类
    Class<? extends MarketingExclusiveClient> marketingExclusiveClientClass = marketingExclusiveClient.getClass();
    // 设置默认限制大小
    marketingExclusiveClient.singleFileLimit=104857600L;
    // 使用反射设置私有字段 requestTemplate
    Field requestTemplateField = marketingExclusiveClientClass.getDeclaredField("requestTemplate");
    requestTemplateField.setAccessible(true);
    requestTemplateField.set(marketingExclusiveClient,"%s://%s/%s");
    return marketingExclusiveClient;
  }

}