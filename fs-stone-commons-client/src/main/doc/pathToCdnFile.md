---
title: Doc
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# Doc

Base URLs:

# Authentication

# 文件系统/Rest/文件操作

## POST Path转静态CDN文件

POST /FilesCdn/pathToCdnFile

> Body 请求参数

```json
{
  "tenantId": 1,
  "employeeId": 0,
  "business": "string",
  "businessUnit": "string",
  "path": "string",
  "name": "string",
  "extension": "string",
  "hashCode": "string",
  "tags": [
    "string"
  ],
  "description": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[PathToCdnFileReq](#schemapathtocdnfilereq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": 0,
  "message": "",
  "data": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RString](#schemarstring)|

# 数据模型

<h2 id="tocS_RString">RString</h2>

<a id="schemarstring"></a>
<a id="schema_RString"></a>
<a id="tocSrstring"></a>
<a id="tocsrstring"></a>

```json
{
  "success": true,
  "code": 0,
  "message": "string",
  "data": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||none|
|code|integer|false|none||none|
|message|string|false|none||none|
|data|string|false|none||none|

<h2 id="tocS_PathToCdnFileReq">PathToCdnFileReq</h2>

<a id="schemapathtocdnfilereq"></a>
<a id="schema_PathToCdnFileReq"></a>
<a id="tocSpathtocdnfilereq"></a>
<a id="tocspathtocdnfilereq"></a>

```json
{
  "tenantId": 1,
  "employeeId": 0,
  "business": "string",
  "businessUnit": "string",
  "path": "string",
  "name": "string",
  "extension": "string",
  "hashCode": "string",
  "tags": [
    "string"
  ],
  "description": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|true|none||文件所属企业|
|employeeId|integer(int64)|true|none||用户ID<br />注：如果是系统调用则传 -10000|
|business|string|true|none||业务线标识（必须申请注册,存在校验）|
|businessUnit|string¦null|false|none||业务线子标识 (允许业务线自行分组)<br />必须由小写字母、数字、下划线、连字符组成,长度不超过32个字符|
|path|string|true|none||支持N|TN|C|TC|
|name|string¦null|false|none||原始文件名|
|extension|string|true|none||当前仅支持jpg、webp、png、jpeg、bmp、js、css、html<br />更多联系请申请添加|
|hashCode|string¦null|false|none||文件Hash（用于幂等性校验）|
|tags|[string]¦null|false|none||打标记（标识业务、标识一类数据、最多10个）<br />每个标签必须由小写字母、数字、下划线、连字符组成，长度不超过20个字符<br />最多10个标签<br />注：可能没有|
|description|string¦null|false|none||描述（描述图片的作用及内容，用于AI合规校验）<br />长度不超过200个字符|

