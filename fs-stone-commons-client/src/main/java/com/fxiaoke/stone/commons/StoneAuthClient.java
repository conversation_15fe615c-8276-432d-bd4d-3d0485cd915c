package com.fxiaoke.stone.commons;

import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.ASKOmit;
import com.fxiaoke.stone.commons.domain.model.ImmOmit;
import com.fxiaoke.stone.commons.domain.model.IncFileQuotaItem;
import com.fxiaoke.stone.commons.domain.model.PolicyOmit;
import com.fxiaoke.stone.commons.domain.model.TingWuOmit;
import com.fxiaoke.stone.commons.domain.model.UsedQuotaOmit;
import java.util.List;

public interface StoneAuthClient {

  /**
   * 根据AccessKey获取SecretKey
   * @param accessKey accessKey
   * @return SecretKey 如果状态为禁用|过期|不存在则返回null
   */
  String getSkByAccessKey(String accessKey) throws StoneCommonClientException;

  /**
   * 根据AccessKey获取Policy
   * @param accessKey accessKey
   * @return PolicyOmit
   */
  PolicyOmit getPolicyByAccessKey(String accessKey) throws StoneCommonClientException;

  /**
   * 根据EA获取Imm配置信息
   * @param ea ea 企业账号
   * @return ImmOmit Imm配置信息
   */
  ImmOmit getImmByEa(String ea) throws StoneCommonClientException;

  /**
   * 获取tingwu 配置
   * @param ea ea
   * @return tingwu 配置
   */
  TingWuOmit getTingWuCredentialByEa(String ea) throws StoneCommonClientException;

  /**
   * 获取tingwu sts认证 配置
   * @param ea ea
   * @return tingwu sts 认证信息 配置
   */
  TingWuOmit getTingWuStsCredentialByEa(String ea) throws StoneCommonClientException;

  /**
   * 根据EA获取Sts SecretKey
   * 注：大附件私有适配模拟ASK
   * @param ea 企业账号
   * @return Sts SecretKey
   */
  ASKOmit getSkBySts(String ea) throws StoneCommonClientException;

  /**
   * 获取企业已使用的文件存储配额
   * @param ea 企业账号
   * @return 已使用配额信息 {@link UsedQuotaOmit}
   * @throws StoneCommonClientException 如果获取失败
   */
  UsedQuotaOmit getUsedQuotaByEa(String ea) throws StoneCommonClientException;

  /**
   * 更新企业已使用的文件存储配额
   * @param business 业务标识
   * @param incFileQuotaItem 配额信息
   * @return true 更新成功，false 更新失败
   * @throws StoneCommonClientException 如果更新失败
   */
  boolean incFileQuota(String business, IncFileQuotaItem incFileQuotaItem) throws StoneCommonClientException;

  /**
   * 批量更新企业已使用的文件存储配额
   * @param business 业务标识
   * @param incFileQuotaItems 配额信息
   *  1.每个文件对应一个UpdateQuotaItem 2.最大支持100个UpdateQuotaItem
   * @return true 更新成功，false 更新失败
   * @throws StoneCommonClientException 如果更新失败
   */
  boolean incFileQuotas(String business, List<IncFileQuotaItem> incFileQuotaItems) throws StoneCommonClientException;
}
