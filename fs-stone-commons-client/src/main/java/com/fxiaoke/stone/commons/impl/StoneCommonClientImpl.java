package com.fxiaoke.stone.commons.impl;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneCommonClient;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignDocumentPreviewUrl;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignDownloadUrl;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignPreviewUrl;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.AcUser;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.fxiaoke.stone.commons.domain.model.FileInfo;
import com.fxiaoke.stone.commons.domain.model.FileUser;
import com.fxiaoke.stone.commons.domain.model.Pair;
import com.fxiaoke.stone.commons.domain.model.RequestTemplate;
import com.fxiaoke.stone.commons.domain.utils.AES256Util;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;

public class StoneCommonClientImpl extends BusinessPresetClient implements StoneCommonClient {
  private RequestTemplate requestTemplate;

  public StoneCommonClientImpl(OkHttpSupport client, BusinessEnum business) {
    super(client, business);
    loadDomainEnvCache();
    registerConfiguration();
  }

  public StoneCommonClientImpl(OkHttpSupport client, BusinessEnum business,
      int credentialsOmitCacheSize) {
    super(client, business, credentialsOmitCacheSize);
    loadDomainEnvCache();
    registerConfiguration();
  }

  public GeneratorSignDownloadUrl.Result generatorDownloadUrl(GeneratorSignDownloadUrl.Arg arg) throws StoneCommonClientException  {
    arg.check();
    String signAcUrl = generatorSignAcUrl(arg.getAcUser(), arg.getFileInfo(), arg.getFileUser(), arg.getExpiresTimestamp(), arg.getBusiness());
    String downloadUrl = compositeDownloadUrl(signAcUrl);
    return GeneratorSignDownloadUrl.Result.of(arg.getFileInfo().getPath(), downloadUrl);
  }

  public GeneratorSignPreviewUrl.Result generatorPreviewUrl(GeneratorSignPreviewUrl.Arg arg) throws StoneCommonClientException {
    arg.check();
    String signAcUrl = generatorSignAcUrl(arg.getAcUser(), arg.getFileInfo(),arg.getFileUser(), arg.getExpiresTimestamp(), arg.getBusiness());
    String previewUrl = compositePreviewUrl(signAcUrl);
    return GeneratorSignPreviewUrl.Result.of(arg.getFileInfo().getPath(), previewUrl);
  }

  public GeneratorSignDocumentPreviewUrl.Result generatorDocumentPreviewUrl(GeneratorSignDocumentPreviewUrl.Arg arg) throws StoneCommonClientException {
    arg.check();
    FileUser fileUser = arg.getFileUser();
    AcUser acUser = arg.getAcUser();
    FileInfo fileInfo = arg.getFileInfo();
    String acid = acUser.getAcid();
    String shareToken = generatorDocumentPreviewUrl(fileUser.getTenantId(), fileUser.getUserId(),
        fileInfo.getPath(),fileInfo.getExtension(),fileUser.getDefaultSecurityGroup(), arg.getExpiresTimestamp());
    EnvEIMapping env = getEnvEIMapping(fileUser.getTenantIdStr(),fileInfo.getPath());
    String format = String.format(requestTemplate.getAnonymityDocumentPreviewRequestTemplate(),
        env.getProtocol(),
        env.getHomeDomain(),
        shareToken,
        acid);
    return GeneratorSignDocumentPreviewUrl.Result.of(fileInfo.getPath(), format);
  }

  private String generatorDocumentPreviewUrl(long tenantId,long userId,String path,String extension,String securityGroup,long ets){
    String raw = Joiner.on(Constants.DELIMITER).join(tenantId, userId, path,extension, securityGroup, ets);
    return AES256Util.encipher(raw);
  }

  private String generatorSignAcUrl(AcUser acUser,FileInfo fileInfo,FileUser fileUser,long ets,String biz) {
    // 生成访问者身份链Acid
    String acid = acUser.getAcid();
    // 生成Fid
    String fid = getFid(fileUser.getTenantId(),fileInfo.getPath());
    // 获取原始文件名与编码后的文件名
    Pair<String, String> filenamePair = fileInfo.getEncodedAndOriginalFilename();
    // 获取访问企业的ASK
    CredentialsOmit credentialsOmit = getCredentialsOmit(acUser.getTenantIdStr(), fileUser.getTenantIdStr(),fileInfo.getPath());
    String accessKey = credentialsOmit.getAccessKey();
    String secretKey = credentialsOmit.getSecretKey();
    // 拼接签名原文
    String raw = SignatureUtil.generatorRaw(fid, acid, ets);
    // 生成访问签名Sig
    String sign = SignatureUtil.getSignatureWithHmacSha1(secretKey, raw);
    // 生成消息摘要Ds
    String ds = SignatureUtil.messageDigest(SignatureUtil.generatorRaw(raw, accessKey, filenamePair.first,biz));
    // 判断企业所在云并获取域名Domain
    EnvEIMapping env = getEnvEIMapping(fileUser.getTenantIdStr(),fileInfo.getPath());
    return String.format(requestTemplate.getAnonymitySignRequestTemplate(),
        env.getProtocol(),
        env.getDomain(),
        env.getContextPath(),
        fid, acid, ets,
        accessKey, filenamePair.second,biz,
        sign, ds);
  }

  private String compositeDownloadUrl(String url) {
    return url + "&acModel=attachment";
  }

  private String compositePreviewUrl(String url) {
    return url + "&acModel=inline";
  }


  private void registerConfiguration() {
    requestTemplate = new RequestTemplate();
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      serverUrl = config.get("cms.stone.commons.client.getPreAskTemplate");
      requestTemplate.setAnonymitySignRequestTemplate(config.get("cms.stone.commons.client.anonymitySignRequestTemplate"));
      requestTemplate.setAnonymityDocumentPreviewRequestTemplate(config.get("cms.stone.commons.client.anonymityDocumentPreviewRequestTemplate"));
    });
  }
}
