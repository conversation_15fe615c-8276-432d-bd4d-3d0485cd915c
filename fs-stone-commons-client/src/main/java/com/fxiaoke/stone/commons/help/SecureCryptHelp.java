package com.fxiaoke.stone.commons.help;

import com.fxiaoke.common.Guard;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;
import java.nio.charset.StandardCharsets;

public class SecureCryptHelp {

  private SecureCryptHelp() {
  }

  private static Guard guard;
  private static Guard signGuard;

  static {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      guard = new Guard(config.getDecrypt("cms.stone.commons.client.iam.encryKey"));
      signGuard = new Guard(config.getDecrypt("cms.stone.commons.client.iam.signEncryKey"));
    });
  }
  public static String encry(String raw) {
    return guard.encode(raw);
  }

  public static String xor(String input, String key) {
    // 关键：使用 ISO-8859-1 编码，确保 byte[] 和 String 长度一一对应
    byte[] inputBytes = input.getBytes(StandardCharsets.ISO_8859_1);
    byte[] keyBytes = key.getBytes(StandardCharsets.ISO_8859_1);

    byte[] outputBytes = new byte[inputBytes.length];

    for (int i = 0; i < inputBytes.length; i++) {
      // 使用循环密钥
      byte keyByte = keyBytes[i % keyBytes.length];
      // 执行XOR操作
      outputBytes[i] = (byte) (inputBytes[i] ^ keyByte);
    }

    // 关键：使用相同的 ISO-8859-1 编码将结果字节数组转回字符串
    return new String(outputBytes, StandardCharsets.ISO_8859_1);
  }

  public static String consistentEncry(String raw) {
    return guard.encode2(raw);
  }

  public static String decry(String ciphertext) {
    return guard.decode(ciphertext);
  }

  public static boolean isEncry(String ciphertext) {
    return ciphertext.startsWith(Constants.ENCRYPTION_PREFIX) &&
        ciphertext.endsWith(Constants.ENCRYPTION_SUFFIX);
  }

  public static String generatorRaw(Object... args) {
    return Joiner.on(Constants.DELIMITER).join(args);
  }

  public static String extractCiphertext(String encCiphertext) {
    return encCiphertext.substring(
        Constants.ENCRYPTION_PREFIX.length(),
        encCiphertext.length() - Constants.ENCRYPTION_SUFFIX.length()
    );
  }

  public static String signEncry(String raw) {
    String ciphertext = signGuard.encode(raw);
    return Constants.ENCRYPTION_PREFIX + ciphertext + Constants.ENCRYPTION_SUFFIX;
  }

  public static String signDecry(String ciphertext) {
    return signGuard.decode(ciphertext);
  }
}