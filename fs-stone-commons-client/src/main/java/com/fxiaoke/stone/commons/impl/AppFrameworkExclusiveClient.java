package com.fxiaoke.stone.commons.impl;

import com.fxiaoke.common.Pair;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.fxiaoke.stone.commons.domain.model.UploadFileOmit;
import com.fxiaoke.stone.commons.domain.utils.DataTimeUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.fxiaoke.stone.commons.help.SecureCryptHelp;
import com.fxiaoke.stone.commons.util.URLSplice;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import java.util.Optional;

public class AppFrameworkExclusiveClient extends BusinessPresetClient implements
    SystemPresetClient {
  public AppFrameworkExclusiveClient(OkHttpSupport client) {
    super(client, BusinessEnum.APP_FRAMEWORK);
    loadDomainEnvCache();
    registerConfiguration();
  }

  public AppFrameworkExclusiveClient(OkHttpSupport client, int credentialsOmitCacheSize) {
    super(client, BusinessEnum.APP_FRAMEWORK, credentialsOmitCacheSize);
    loadDomainEnvCache();
    registerConfiguration();
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String signature, long expireTime, String filename,
      String extension) throws StoneCommonClientException {
    return generateUrl(tenantId,userId,outTenantId,outUserId,upstreamOwnerId,AuthModel.SIGN,path,signature,expireTime,filename,extension,false);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId,AuthModel authModel, String path, String signature, long expireTime,
      String filename, String extension) throws StoneCommonClientException {
    return generateUrl(tenantId,userId,outTenantId,outUserId,upstreamOwnerId,authModel,path,signature,expireTime,filename,extension,false);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId,
      String path, String signature, long expireTime,
      String filename, String extension,
      boolean isAvatar) throws StoneCommonClientException {
    return generateUrl(tenantId,userId,outTenantId,outUserId,upstreamOwnerId,AuthModel.SIGN,path,signature,expireTime,filename,extension,isAvatar);
  }

  @Override
  public String generateCFileAccessUrl(String tenantId,String userId,
      String path, String signature,String extension,
      String business) throws StoneCommonClientException {
    checkCFileStr(path);
    // 获取文件所在企业ID
    String fileEi = signature.substring(0, signature.indexOf(Constants.DELIMITER));
    // 拼接访问者身份链
    String Acid = getAcid(fileEi, tenantId, userId);
    // 生成消息摘要
    String Ds = SignatureUtil.messageDigest(SignatureUtil.generatorRaw(path, Acid,extension,business));
    // 判断企业所在云并获取域名
    EnvEIMapping env = getEnvEIMapping(fileEi,path);
    // 生成URL并返回
    return URLSplice.generateCFileAccessUrl(env.getProtocol(),env.getCdnDomain(),env.getCdnContextPath(),path,Acid,extension,business,Ds);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String fileTenantId, String filename, String extension,
      long expireTime, String business) throws StoneCommonClientException {
    // 生成Fid 即文件信息
    String Fid = SecureCryptHelp.encry(SignatureUtil.generatorRaw(fileTenantId, path));
    // 获取访问者身份链
    String Acid = getAcid(tenantId, userId, outTenantId, outUserId,upstreamOwnerId).second;
    // 获取文件名 并进行URL编码
    Pair<String, String> FnPair = getFilename(extension, filename);
    // 生成过期时间戳
    long Ets = DataTimeUtil.getExpiresTimestamp(expireTime);
    // 获取访问签名ASK
    CredentialsOmit credentialsOmit = getCredentialsOmit(tenantId, fileTenantId,path);
    String accessKey = credentialsOmit.getAccessKey();
    String secretKey = credentialsOmit.getSecretKey();
    // 拼接签名原文
    String raw = SignatureUtil.generatorRaw(Fid, Acid, Ets);
    // 生成访问签名
    String Sig = SignatureUtil.getSignatureWithHmacSha1(secretKey, raw);
    // 生成消息摘要
    String Ds = SignatureUtil.messageDigest(SignatureUtil.generatorRaw(raw, accessKey, FnPair.first,business));
    // 判断企业所在云并获取域名
    EnvEIMapping env = getEnvEIMapping(fileTenantId,path);
    // 生成URL并返回
    return URLSplice.generateAnonymitySign(env.getProtocol(),env.getDomain(),env.getContextPath(),Fid,Acid,Ets,accessKey,FnPair.second,business,Sig,Ds);
  }

  private String generateUrl(String tenantId, String userId, String outTenantId,
      String outUserId, String upstreamOwnerId,
      AuthModel authModel, String path, String signature, long expireTime,
      String filename, String extension, boolean isAvatar) throws StoneCommonClientException {
    // 参数校验
    if (signature == null || signature.isEmpty()) {
      throw new StoneCommonClientException("Signature cannot be null or empty", 400);
    }
    if (path == null || path.isEmpty()) {
      throw new StoneCommonClientException("Path cannot be null or empty", 400);
    }

    // 适配自动解密
    Optional<String> stringOptional = decryptSignature(tenantId, signature);
    if (!stringOptional.isPresent()){
      throw new StoneCommonClientException("Signature Invalid", 400);
    }
    signature=stringOptional.get();

    // 生成Fid 即文件信息
    String fid = getFid(authModel, SignatureUtil.generatorRaw(signature, path));
    // 获取文件名 并进行URL编码
    Pair<String, String> fnPair = getFilename(extension, filename);
    Pair<Boolean, String> acidPair = getAcid(tenantId, userId, outTenantId, outUserId,
        upstreamOwnerId, authModel);
    // 头像文件进行特殊处理
    if (isAvatar) {
      return generateAvatarUrl(fid, acidPair.second, fnPair.first);
    }
    // 获取文件所在企业ID
    int delimiterIndex = signature.indexOf(Constants.DELIMITER);
    if (delimiterIndex == -1) {
      throw new StoneCommonClientException("Invalid signature format", 400);
    }
    String fileEi = signature.substring(0, delimiterIndex);
    // 获取访问签名ASK
    CredentialsOmit credentialsOmit = getCredentialsOmit(tenantId, fileEi, path);
    String accessKey = credentialsOmit.getAccessKey();
    String secretKey = credentialsOmit.getSecretKey();
    // 生成过期时间戳
    long ets = DataTimeUtil.getExpiresTimestamp(expireTime);
    // 拼接签名原文
    String raw = getSignRaw(fid, acidPair.second, ets, authModel);
    // 生成访问签名
    String sig = SignatureUtil.getSignatureWithHmacSha1(secretKey, raw);
    // 生成消息摘要
    String ds = SignatureUtil.messageDigest(
        SignatureUtil.generatorRaw(raw, accessKey, fnPair.first));
    // 判断企业所在云并获取域名
    EnvEIMapping env = getEnvEIMapping(fileEi, path);
    return fastBuildUrl(acidPair, authModel, env, fid, ets, accessKey, fnPair.second, sig, ds);

  }

  private String getFid(AuthModel authModel,String raw) {
    switch (authModel){
      case COOKIE:
      case COOKIE_ALL: return SecureCryptHelp.consistentEncry(raw);
      default: return SecureCryptHelp.encry(raw);
    }
  }

  private String getSignRaw(String fid,String acid,long ets,AuthModel authModel){
    // Cookie 验证无需过期时间
    if (authModel==AuthModel.COOKIE||authModel==AuthModel.COOKIE_ALL){
      return SignatureUtil.generatorRaw(fid, acid);
    }
    return SignatureUtil.generatorRaw(fid, acid, ets);
  }

  private String fastBuildUrl(Pair<Boolean, String> acidPair, AuthModel authModel, EnvEIMapping env,
      String fid, long ets, String accessKey, String fn, String sig, String ds) {
    switch (authModel) {
      case SIGN:
        return URLSplice.generateSignUrl(env.getProtocol(), env.getDomain(), env.getContextPath(),
            fid, acidPair.second, ets, accessKey, fn, sig, ds);
      case SIGN_COOKIE:
        if (Boolean.TRUE.equals(acidPair.first)) {
          return URLSplice.generateSignEm6Url(env.getProtocol(), env.getDomain(),
              env.getContextPath(), fid, acidPair.second, ets, accessKey, fn, sig, ds);
        } else {
          return URLSplice.generateSignFsAuthXCUrl(env.getProtocol(), env.getDomain(),
              env.getContextPath(), fid, acidPair.second, ets, accessKey, fn, sig, ds);
        }
      case COOKIE:
        if (Boolean.TRUE.equals(acidPair.first)) {
          return URLSplice.generateCookieEm6Url(env.getProtocol(), env.getDomain(),
              env.getContextPath(), fid, acidPair.second, accessKey, fn, sig, ds);
        } else {
          return URLSplice.generateCookieFsAuthXCUrl(env.getProtocol(), env.getDomain(),
              env.getContextPath(), fid, acidPair.second, accessKey, fn, sig, ds);
        }
      case COOKIE_ALL:
        return URLSplice.generateCookieUrl(env.getProtocol(), env.getDomain(), env.getContextPath(),
            fid, acidPair.second, accessKey, fn, sig, ds);
      default:
        throw new StoneCommonClientException("Not support authModel", 400);
    }
  }

  @Override
  public UploadFileOmit generateUploadFileOmit(String tenantId, long expireTime, FileResourceEnum resourceType,boolean isStreamUpload,String filename, String extension,
      int fileSize) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generatePutUrl method,Please use MarketingExclusiveClient", 400);
  }

  @Override
  public String signature(long enterpriseId, String path) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support signature method,Please use MetadataExclusiveClient", 400);
  }

  @Override
  public Optional<String> generateSignature(long enterpriseId, String path)
      throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support signature method,Please use MetadataExclusiveClient", 400);
  }

  // 生成头像文件请求URL,因为头像文件没有隐私属性所以无需进行严格的权限校验且为了利用缓存不指定过期时间
  private String generateAvatarUrl(String Fid,String Acid,String Fn){
    // 拼接签名原文
    String raw = SignatureUtil.generatorRaw(Fid, Acid, Fn);
    // 生成消息摘要
    String Ds = SignatureUtil.messageDigest(raw);
    EnvEIMapping env = getFacishareEnvEIMapping();
    return URLSplice.generateAvatarUrl(env.getProtocol(),env.getDomain(),env.getContextPath(),Fid,Acid,Fn,Ds);
  }

  private String getAcid(String fileTenantId,String tenantId, String userId){
    if (Strings.isNullOrEmpty(fileTenantId)){
      throw new StoneCommonClientException("fileTenantId cannot be null or empty", 400);
    }
    if (Strings.isNullOrEmpty(tenantId)){
      throw new StoneCommonClientException("tenantId cannot be null or empty", 400);
    }
    if (Strings.isNullOrEmpty(userId)){
      throw new StoneCommonClientException("userId cannot be null or empty", 400);
    }
    return String.join(".", fileTenantId,tenantId,userId);
  }

  private void checkCFileStr(String path){
    if (Strings.isNullOrEmpty(path)) {
      throw new StoneCommonClientException("path cannot be null or empty", 400);
    }
    if (!(path.startsWith("C_") || path.startsWith("TC_"))) {
      throw new StoneCommonClientException("path must start with C_ or TC_", 400);
    }
  }


  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      serverUrl = config.get("cms.stone.commons.client.getPreAskTemplate");
    });
  }

}
