package com.fxiaoke.stone.commons;

import com.fxiaoke.stone.commons.domain.api.PathToCdnFile;
import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.io.IOException;
import java.io.InputStream;

/**
 * Stone 文件服务客户端接口
 *
 * <p>提供文件上传功能，支持流式上传和缓存上传两种模式。</p>
 */
public interface StoneFileClient {

  /**
   * 上传N|C|TC|TN类型文件
   *
   * <p><strong>资源管理约定</strong>
   * <ul>
   *   <li><strong>InputStream</strong>: 由调用者负责创建和关闭，本方法只负责读取</li>
   *   <li><strong>临时文件</strong>: 路径由调用者创建，由本方法创建和清理</li>
   *   <li><strong>HTTP连接</strong>: 由OkHttp客户端管理</li>
   * </ul>
   *
   * @param arg 上传参数
   * @param cacheConfig  缓存配置</br>
   *  cacheConfig.maxCacheSize 最大缓存大小（字节），当文件大小未知时使用 </br>
   *  cacheConfig.bufferSize 缓冲区大小（字节） </br>
   *  cacheConfig.cachePath  缓存文件路径，必须有读写权限 </br>
   * @param stream 文件输入流，调用者负责关闭
   * @return 上传结果
   * @throws StoneCommonClientException,IOException 如果上传过程中发生错误,由调用者处理</br>
   * StoneCommonClientException 用于处理业务逻辑错误，如参数验证失败、文件大小超出限制等</br>
   * IOException 用于处理技术层面的错误，如网络异常、IO错误等
   */
  StoneUploadFile.Result uploadFile(StoneUploadFile.Arg arg,
      StoneUploadFile.CacheConfig cacheConfig,
      InputStream stream) throws StoneCommonClientException, IOException;

  PathToCdnFile.Result pathToCdnFile(PathToCdnFile.Arg arg);
}
