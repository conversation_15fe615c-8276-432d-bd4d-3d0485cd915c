package com.fxiaoke.stone.commons.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneAuthClient;
import com.fxiaoke.stone.commons.domain.R;
import com.fxiaoke.stone.commons.domain.api.GetImmByEa;
import com.fxiaoke.stone.commons.domain.api.GetPolicyByAccessKey;
import com.fxiaoke.stone.commons.domain.api.GetPolicyByAccessKey.Result;
import com.fxiaoke.stone.commons.domain.api.GetSkByAKey;
import com.fxiaoke.stone.commons.domain.api.GetTingWuByEa;
import com.fxiaoke.stone.commons.domain.api.GetUsedQuota;
import com.fxiaoke.stone.commons.domain.api.IncFileUsedQuotaArg;
import com.fxiaoke.stone.commons.domain.api.IncFileUsedQuotaArgs;
import com.fxiaoke.stone.commons.domain.api.IncFileUsedQuotaItem;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.constant.PolicyEffectTimeEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.*;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import com.fxiaoke.stone.commons.util.CallServiceUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;

@Slf4j
public class StoneAuthClientImpl implements StoneAuthClient {

  private String askServerUrl;
  private String askStsServerUrl;
  private String policyServerUrl;
  private String immServerUrl;
  private String usedQuotaServerUrl;
  private String incrementUsedQuotaUrl;
  private String incrementUsedQuotasUrl;
  final OkHttpSupport client;
  private int cacheSize;
  private LoadingCache<String, String> askCache;
  private LoadingCache<String, PolicyOmit> policyOmitCache;
  private LoadingCache<String, ImmOmit> immOmitCache;
  private LoadingCache<String, TingWuOmit> tingWuOmitCache;
  private LoadingCache<String, TingWuOmit> tingWuStsOmitCache;
  private LoadingCache<String, ASKOmit> askStsCache;
  private LoadingCache<String, UsedQuotaOmit> usedQuotaCache;
  private String tingWuOmitUrl;
  private String tingWuStsOmitUrl;

  private static final MediaType json = MediaType.parse("application/json; charset=utf-8");

  public StoneAuthClientImpl(OkHttpSupport client) {
    this.client = client;
    loadCache();
    registerConfiguration();
  }

  public StoneAuthClientImpl(OkHttpSupport client, int cacheSize) {
    this.client = client;
    this.cacheSize = cacheSize;
    loadCache();
    registerConfiguration();
  }

  @Override
  public String getSkByAccessKey(String accessKey) {
    return askCache.getUnchecked(accessKey);
  }

  private String getSk(String accessKey) {
    String requestUrl = String.format(askServerUrl, accessKey);
    ASKOmit askOmit = getASKOmit(requestUrl);
    return askOmit.getSecretKey();
  }

  @Override
  public PolicyOmit getPolicyByAccessKey(String accessKey) {
    return policyOmitCache.getUnchecked(accessKey);
  }

  private PolicyOmit fetchPolicyOmit(String accessKey) {
    String requestUrl = String.format(policyServerUrl, accessKey);
    String resultStr = CallServiceUtil.get(client, requestUrl);
    R<GetPolicyByAccessKey.Result> result = CallServiceUtil.formJson(resultStr,
        GetPolicyByAccessKey.Result.class);
    PolicyOmit policyOmit = new PolicyOmit();
    Result data = result.getData();
    policyOmit.setEffect(data.isEffect());
    String effectTimeType = data.getEffectTimeType();
    policyOmit.setEffectTimeType(effectTimeType);
    if (PolicyEffectTimeEnum.match(effectTimeType, PolicyEffectTimeEnum.PERMANENT)) {
      EffectTime effectTime = new EffectTime();
      effectTime.setAfterTime(data.getEffectTime().getAfterTime());
      effectTime.setBeforeTime(data.getEffectTime().getBeforeTime());
      policyOmit.setEffectTime(effectTime);
    }
    policyOmit.setConditions(data.getConditions());
    policyOmit.setActions(data.getActions());
    policyOmit.setResource(data.getResource());
    policyOmit.setGroups(data.getGroups());
    policyOmit.setTourists(data.getTourists());
    return policyOmit;
  }

  @Override
  public ImmOmit getImmByEa(String ea) {
    return immOmitCache.getUnchecked(ea);
  }

  private ImmOmit fetchImmOmit(String ea) {
    String requestUrl = String.format(immServerUrl, ea);
    String resultStr = CallServiceUtil.get(client, requestUrl);
    R<GetImmByEa.Result> result = CallServiceUtil.formJson(resultStr, GetImmByEa.Result.class);
    ImmOmit immOmit = new ImmOmit();
    GetImmByEa.Result data = result.getData();
    immOmit.setProjectName(data.getProjectName());
    immOmit.setAccessKey(data.getAccessKey());
    immOmit.setSecretKey(data.getSecretKey());
    immOmit.setBucketName(data.getBucketName());
    return immOmit;
  }

  @Override
  public TingWuOmit getTingWuCredentialByEa(String ea) {
    return tingWuOmitCache.getUnchecked(ea);
  }

  private TingWuOmit fetchTingWuOmit(String ea) {
    String requestUrl = String.format(tingWuOmitUrl, ea);
    return getTingWuOmit(requestUrl);
  }

  @Override
  public TingWuOmit getTingWuStsCredentialByEa(String ea) {
    return tingWuStsOmitCache.getUnchecked(ea);
  }

  private TingWuOmit fetchTingWuStsOmit(String ea) {
    String requestUrl = String.format(tingWuStsOmitUrl, ea);
    return getTingWuOmit(requestUrl);
  }

  private TingWuOmit getTingWuOmit(String requestUrl) {
    String resultStr = CallServiceUtil.get(client, requestUrl);
    R<GetTingWuByEa.Result> result = CallServiceUtil.formJson(resultStr,
        GetTingWuByEa.Result.class);
    TingWuOmit tingWuOmit = new TingWuOmit();
    GetTingWuByEa.Result data = result.getData();
    tingWuOmit.setAccessKey(data.getAccessKey());
    tingWuOmit.setSecretKey(data.getSecretKey());
    tingWuOmit.setEndPoint(data.getEndPoint());
    tingWuOmit.setAppKey(data.getAppKey());
    tingWuOmit.setStsToken(data.getStsToken());
    tingWuOmit.setTransResultOssBucket(data.getTransResultOssBucket());
    return tingWuOmit;
  }

  @Override
  public ASKOmit getSkBySts(String ea) {
    return askStsCache.getUnchecked(ea);
  }

  private ASKOmit fetchSkBySts(String ea) {
    String requestUrl = String.format(askStsServerUrl, ea, BusinessEnum.STS.getBusiness());
    return getASKOmit(requestUrl);
  }

  @Override
  public UsedQuotaOmit getUsedQuotaByEa(String ea) throws StoneCommonClientException {
    return usedQuotaCache.getUnchecked(ea);
  }

  @Override
  public boolean incFileQuota(String business, IncFileQuotaItem incFileQuotaItem)
      throws StoneCommonClientException {

    validateParameters(business, incFileQuotaItem);

    IncFileUsedQuotaItem item = new IncFileUsedQuotaItem();
    item.setEa(incFileQuotaItem.getEa());
    item.setQuota(incFileQuotaItem.getQuota());
    item.setCount(incFileQuotaItem.getQuota() > 0 ? 1 : -1);
    IncFileUsedQuotaArg arg = new IncFileUsedQuotaArg();
    arg.setItem(item);
    arg.setBusiness(business);

    return incFileQuotas(incrementUsedQuotaUrl,JSON.toJSONString(arg));
  }

  @Override
  public boolean incFileQuotas(String business, List<IncFileQuotaItem> incFileQuotaItems)
      throws StoneCommonClientException {

    validateParameters(business, incFileQuotaItems);

    IncFileUsedQuotaArgs updateQuotaBatchArg = new IncFileUsedQuotaArgs();
    updateQuotaBatchArg.setBusiness(business);
    updateQuotaBatchArg.setItems(mergeQuotaItems(incFileQuotaItems));

    return incFileQuotas(incrementUsedQuotasUrl,JSON.toJSONString(updateQuotaBatchArg));
  }

  private void validateParameters(String business, IncFileQuotaItem incFileQuotaItem)
      throws StoneCommonClientException {
    if (Strings.isNullOrEmpty(business) || incFileQuotaItem == null) {
      throw new StoneCommonClientException("business||updateQuotaItems parameter can not be blank",
          400);
    }
    if (Strings.isNullOrEmpty(incFileQuotaItem.getEa())) {
      throw new StoneCommonClientException("ea parameter in incFileQuotaItem can not be blank", 400);
    }
  }

  private void validateParameters(String business, List<IncFileQuotaItem> incFileQuotaItems)
      throws StoneCommonClientException {
    if (Strings.isNullOrEmpty(business) || incFileQuotaItems == null || incFileQuotaItems.isEmpty()) {
      throw new StoneCommonClientException("business||incFileQuotaItems parameter can not be blank", 400);
    }
    if (incFileQuotaItems.size() > 100) {
      throw new StoneCommonClientException("incFileQuotaItems size can not be greater than 100", 400);
    }
  }

  /**
   * 合并配额项，按EA分组
   * @param incFileQuotaItems 待合并的配额项列表
   * @return 合并后的配额项列表
   * @throws StoneCommonClientException 如果EA为空或其他参数错误
   */
  private List<IncFileUsedQuotaItem> mergeQuotaItems(List<IncFileQuotaItem> incFileQuotaItems)
      throws StoneCommonClientException {
    // 使用HashMap即可，无需LinkedHashMap
    Map<String, IncFileUsedQuotaItem> itemMap = new HashMap<>(incFileQuotaItems.size());

    for (IncFileQuotaItem item : incFileQuotaItems) {
      String ea = item.getEa();
      if (Strings.isNullOrEmpty(ea)) {
        throw new StoneCommonClientException("ea parameter in incFileQuotaItems can not be blank", 400);
      }

      long quota = item.getQuota();
      long fileCount = quota > 0 ? 1L : -1L;

      itemMap.merge(ea,
          new IncFileUsedQuotaItem(ea, quota, fileCount),
          (existing, newItem) -> {
            existing.setQuota(existing.getQuota() + newItem.getQuota());
            existing.setCount(existing.getCount() + newItem.getCount());
            return existing;
          });
    }

    return new ArrayList<>(itemMap.values());
  }

  private boolean incFileQuotas(String endpoint,String arg) {
    RequestBody requestBody = RequestBody.create(arg, json);
    String resultStr = CallServiceUtil.post(client, endpoint, requestBody);
    R<Boolean> result = CallServiceUtil.formJson(resultStr, Boolean.class);
    if (!result.isSuccess()) {
      throw new StoneCommonClientException(result.getMessage(), result.getCode(), arg);
    }
    return result.getData();
  }

  private UsedQuotaOmit fetchUsedQuota(String ea) {
    String requestUrl = String.format(usedQuotaServerUrl, ea);
    String resultStr = CallServiceUtil.get(client, requestUrl);
    R<GetUsedQuota.Result> result = CallServiceUtil.formJson(resultStr, GetUsedQuota.Result.class);
    GetUsedQuota.Result data = result.getData();
    UsedQuotaOmit usedQuotaOmit = new UsedQuotaOmit();
    usedQuotaOmit.setEa(data.getEa());
    usedQuotaOmit.setUsedQuota(data.getUsedQuota());
    usedQuotaOmit.setLastUpdate(data.getLastUpdate());
    return usedQuotaOmit;
  }

  private ASKOmit getASKOmit(String requestUrl) {
    String resultStr = CallServiceUtil.get(client, requestUrl);
    R<GetSkByAKey.Result> result = CallServiceUtil.formJson(resultStr, GetSkByAKey.Result.class);
    return new ASKOmit(result.getData().getAccessKey(), result.getData().getSecretKey());
  }

  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      askServerUrl = config.get("cms.stone.commons.client.getAskTemplate");
      policyServerUrl = config.get("cms.stone.commons.client.getPolicyTemplate");
      immServerUrl = config.get("cms.stone.commons.client.getImmTemplate");
      tingWuOmitUrl = config.get("cms.stone.commons.client.getTingWuOmitTemplate");
      tingWuStsOmitUrl = config.get("cms.stone.commons.client.getTingWuStsOmitTemplate");
      askStsServerUrl = config.get("cms.stone.commons.client.getAskStsTemplate");
      usedQuotaServerUrl = config.get("cms.stone.commons.client.getUsedQuotaTemplate");
      incrementUsedQuotaUrl = config.get("cms.stone.commons.client.incrementUsedQuotaTemplate");
      incrementUsedQuotasUrl = config.get("cms.stone.commons.client.incrementUsedQuotasTemplate");
    });
  }

  private void loadCache() {
    askCache = CacheBuilder.newBuilder().maximumSize(cacheSize)
        .build(new CacheLoader<String, String>() {
          @Nonnull
          @Override
          public String load(@Nonnull String key) {
            return getSk(key);
          }
        });
    policyOmitCache = CacheBuilder.newBuilder().maximumSize(cacheSize)
        .build(new CacheLoader<String, PolicyOmit>() {
          @Nonnull
          @Override
          public PolicyOmit load(@Nonnull String key) {
            return fetchPolicyOmit(key);
          }
        });
    immOmitCache = CacheBuilder.newBuilder().maximumSize(cacheSize)
        .build(new CacheLoader<String, ImmOmit>() {
          @Nonnull
          @Override
          public ImmOmit load(@Nonnull String key) {
            return fetchImmOmit(key);
          }
        });
    tingWuOmitCache = CacheBuilder.newBuilder().maximumSize(cacheSize)
        .build(new CacheLoader<String, TingWuOmit>() {
          @Nonnull
          @Override
          public TingWuOmit load(@Nonnull String key) {
            return fetchTingWuOmit(key);
          }
        });
    tingWuStsOmitCache = CacheBuilder.newBuilder().maximumSize(cacheSize)
        .build(new CacheLoader<String, TingWuOmit>() {
          @Nonnull
          @Override
          public TingWuOmit load(@Nonnull String key) {
            return fetchTingWuStsOmit(key);
          }
        });
    askStsCache = CacheBuilder.newBuilder().maximumSize(cacheSize)
        .build(new CacheLoader<String, ASKOmit>() {
          @Nonnull
          @Override
          public ASKOmit load(@Nonnull String key) {
            return fetchSkBySts(key);
          }
        });
    usedQuotaCache = CacheBuilder
        .newBuilder()
        .maximumSize(cacheSize)
        // 写入后一小时失效
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build(new CacheLoader<String, UsedQuotaOmit>() {
          @Nonnull
          @Override
          public UsedQuotaOmit load(@Nonnull String key) {
            return fetchUsedQuota(key);
          }
        });
  }

}
