package com.fxiaoke.stone.commons;

import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.UploadFileOmit;
import java.util.Optional;

public interface SystemPresetClient {
  /**
   * 生成文件元数据签名,该签名永久有效 (支持跨云,支持用户自授权);
   * 该方法已废弃,请使用{@link #generateSignature(long enterpriseId, String path)}
   * 作用:业务在保存元数据时,对文件进行签名;
   * 使用场景:该签名可用于后续下载文件时的鉴权或企业间文件分享;
   * @param enterpriseId 企业ID;
   * @param path 文件path;
   * @return 签名
   */
  @Deprecated
  String signature(long enterpriseId,String path) throws StoneCommonClientException;

  /**
   * 生成文件元数据签名,该签名永久有效 (支持跨云,支持用户自授权);
   * 作用:业务在保存元数据时,对文件进行签名;
   * 使用场景:该签名可用于后续下载文件时的鉴权或企业间文件分享;
   * @param enterpriseId 企业ID;
   * @param path 文件path;
   * @return 签名 当path不合法时返回空
   */
  Optional<String> generateSignature(long enterpriseId, String path) throws StoneCommonClientException;

  /**
   * 签名加密方法(注:不建议缓存加密签名,签名每次加密后都不同);
   * @param said 言(一般是tenantId);
   * @param signature 文件元数据签名;
   * @return 加密后的字符串，格式为Enc(加密内容)
   * 注：已加密的签名不会再次加密将原样返回,非法参数返回Optional.empty()
   * 解密请使用以下方法:{@link #decryptSignature(String said,String encCiphertext)}
   */
  Optional<String> encryptSignature(String said,String signature) throws StoneCommonClientException;

  /**
   * 加密签名解密方法
   * @param said 用于验证的言(一般是tenantId);
   * @param encCiphertext 加密的签名;
   * @return 返回解密后的原始signature
   * 注：非加密的签名会被原样返回,非法参数返回Optional.empty()
   * 加密请使用以下方法:{@link #encryptSignature(String said,String signature)}
   */
  Optional<String> decryptSignature(String said,String encCiphertext) throws StoneCommonClientException;

  /**
   * 生成自签名的下载链接 (支持跨云);
   * 该方法已废弃,请使用{@link #generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId, AuthModel authModel,
   *       String path, String signature, long expireTime,
   *       String filename, String extension)}
   * 作用:用以在业务给前端返回文件访问链接;
   * @param tenantId 企业ID,发起访问请求的企业ID (上游企业请求） 不能为空;
   * @param userId   用户ID,发起访问请求的用户ID (上游企业请求） 可以为空或null;
   * @param outTenantId 下游企业ID,发起访问请求的企业ID (下游企业请求）可以为空或null;
   * @param outUserId 下游用户ID,发起访问请求的用户ID (下游企业请求） 可以为空或null;
   * @param upstreamOwnerId 下游企业的上游负责人ID (下游企业请求） 可以为空或null;
   * 允许的组合:tenantId+userId、tenantId+upstreamOwnerId+outTenantId+outUserId;
   *           当全部参数均不为空时，默认为下游企业请求;
   *
   * @param path 文件path 支持 N|C|A|G|TN|TC|TA 类型;
   * @param signature 元数据签名;
   * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天;
   *
   * @param filename 完整文件名 即携带扩展名;
   * 注:SDK会自动对文件名进行URL编码,调用方无需关注;
   *    SDK会自动处理filename可能为空的情况,默认为YYYY-MM-DD-HH+扩展名;
   *    如果文件命中包含不支持URL编码的字符同样使用默认文件名;
   * @param extension 文件扩展名 不需要带点号 （注: SDK会自动处理extensions可能为空的情况,默认为 bin）
   *
   * @return 下载链接
   */
  @Deprecated
  String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId,
      String path, String signature, long expireTime,
      String filename, String extension) throws StoneCommonClientException;

  /**
   * 生成自签名的下载链接 (支持跨云);
   * 作用:用以在业务给前端返回文件访问链接;
   * @param tenantId 企业ID,发起访问请求的企业ID (上游企业请求） 不能为空;
   * @param userId   用户ID,发起访问请求的用户ID (上游企业请求） 可以为空或null;
   * @param outTenantId 下游企业ID,发起访问请求的企业ID (下游企业请求）可以为空或null;
   * @param outUserId 下游用户ID,发起访问请求的用户ID (下游企业请求） 可以为空或null;
   * @param upstreamOwnerId 下游企业的上游负责人ID (下游企业请求） 可以为空或null;
   * 允许的组合:tenantId+userId、tenantId+upstreamOwnerId+outTenantId+outUserId;
   *           当全部参数均不为空时，默认为下游企业请求;
   * @param authModel 认证模式 {@link AuthModel}
   *                  - sign:仅认证签名(适合URL下发后实时触发的场景)
   *                  - cookie:仅认证Cookie-FSAuthXC、Cookie-ERInfo  (适合一些对隐私要求较高的场景)
   *                  - sign_cookie:优先认证签名,签名过期候补认证cookie,Cookie-FSAuthXC、Cookie-ERInfo (适合URL下发后触发具有延迟性的场景)
   * @param path 文件path 支持 N|C|A|G|TN|TC|TA 类型;
   * @param signature 元数据签名;
   * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天;
   * @param filename 完整文件名 即携带扩展名;
   * 注:SDK会自动对文件名进行URL编码,调用方无需关注;
   *    SDK会自动处理filename可能为空的情况,默认为YYYY-MM-DD-HH+扩展名;
   *    如果文件命中包含不支持URL编码的字符同样使用默认文件名;
   * @param extension 文件扩展名 不需要带点号 （注: SDK会自动处理extensions可能为空的情况,默认为 bin）
   *
   * @return 下载链接
   */
  String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId,AuthModel authModel,
      String path, String signature, long expireTime,
      String filename, String extension) throws StoneCommonClientException;

  /**
   * 生成自签名的下载链接 (支持跨云);
   * 该方法已废弃,请使用{@link #generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId, AuthModel authModel,
   *       String path, String signature, long expireTime,
   *       String filename, String extension)}
   * 作用:用以在业务给前端返回文件访问链接;
   * @param tenantId 企业ID,发起访问请求的企业ID (上游企业请求） 不能为空;
   * @param userId   用户ID,发起访问请求的用户ID (上游企业请求） 可以为空或null;
   * @param outTenantId 下游企业ID,发起访问请求的企业ID (下游企业请求）可以为空或null;
   * @param outUserId 下游用户ID,发起访问请求的用户ID (下游企业请求） 可以为空或null;
   * @param upstreamOwnerId 下游企业的上游负责人ID (下游企业请求） 可以为空或null;
   * 允许的组合:tenantId+userId、tenantId+upstreamOwnerId+outTenantId+outUserId;
   *           当全部参数均不为空时，默认为下游企业请求;
   *
   * @param path 文件path 支持 N|C|A|G|TN|TC|TA 类型;
   * @param signature 元数据签名;
   * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天;
   *
   * @param filename 完整文件名 即携带扩展名;
   * 注:SDK会自动对文件名进行URL编码,调用方无需关注;
   *    SDK会自动处理filename可能为空的情况,默认为YYYY-MM-DD-HH+扩展名;
   *    如果文件命中包含不支持URL编码的字符同样使用默认文件名;
   * @param extension 文件扩展名 不需要带点号 （注: SDK会自动处理extensions可能为空的情况,默认为 bin）
   *
   * @param isAvatar 是否为头像文件,头像文件会生成特殊URL (头像文件与企业文件均使用N_作为Path前缀,所以需要额外标识头像文件);
   * @return 下载链接
   */
  @Deprecated
  String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId,
      String path, String signature, long expireTime,
      String filename, String extension,
      boolean isAvatar) throws StoneCommonClientException;

  /**
   * 生成CDN加速的访问链接(仅支持C|TC类型文件);
   * @param tenantId 企业ID,发起访问请求的企业ID 不能为空;
   * @param path 文件path 支持 C|TC 类型;
   * @param signature 元数据签名;
   * @param extension 文件扩展名 不需要带.号;
   * @param business 业务线标识(注：请勿使用特殊字符,长度不超过64个字符);
   * @return 下载链接
   * @throws StoneCommonClientException 生成CDN加速访问链接失败时抛出异常
   */
  String generateCFileAccessUrl(String tenantId,String userId,
      String path, String signature,String extension,
      String business) throws StoneCommonClientException;

  /**
   * 生成匿名访问的下载链接
   * 作用:用以在业务给前端返回文件访问链接;
   * @param tenantId 企业ID,发起访问请求的企业ID (上游企业请求） 不能为空;
   * @param userId   用户ID,发起访问请求的用户ID (上游企业请求） 可以为空或null;
   * @param outTenantId 下游企业ID,发起访问请求的企业ID (下游企业请求）可以为空或null;
   * @param outUserId 下游用户ID,发起访问请求的用户ID (下游企业请求） 可以为空或null;
   * @param upstreamOwnerId 下游企业的上游负责人ID (下游企业请求） 可以为空或null;
   * 允许的组合:tenantId+userId、tenantId+upstreamOwnerId+outTenantId+outUserId;
   *           当全部参数均不为空时，默认为下游企业请求;
   *
   * @param path 文件path 支持 N|C|A|G|TN|TC|TA 类型;
   * @param fileTenantId 文件所属租户ID;
   * @param filename 完整文件名 即携带扩展名;
   * 注:SDK会自动对文件名进行URL编码,调用方无需关注;
   *    SDK会自动处理filename可能为空的情况,默认为YYYY-MM-DD-HH+扩展名;
   *    如果文件命中包含不支持URL编码的字符同样使用默认文件名;
   * @param extension 文件扩展名 不需要带点号 （注: SDK会自动处理extensions可能为空的情况,默认为 bin）
   *
   * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天;
   * @param business 业务线标识(注：请勿使用特殊字符,长度不超过64个字符);
   * @return 下载链接
   */
  String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId,
      String path, String fileTenantId,String filename, String extension,
      long expireTime,String business) throws StoneCommonClientException;

  /**
   * 获取签名及上传接口信息 (不支持跨云);
   * @param tenantId 企业ID,发起访问请求的企业ID,不能为空;
   * @param expireTime 从当前时间起有效时间 最小为60秒 最大为604800秒即7天;
   * @param resourceType 文件资源类型,不能为空;如N、TN、C、TC;
   * 注: C|TC 类型为无隐私CDN加速文件,要求文件类型必须为图片,且仅支持JPG、JPEG、PNG、GIF、BMP、WEBP类型;
   *     如果文件实际类型为其他类型,请使用N|TN类型,否则即使指定为C|TC类型,上传时也会被转换为N|TN类型;
   * @param isStreamUpload 是否为流式上传,默认为false;
   * @param filename 完整文件名 即携带扩展名;
   * 注:SDK会自动对文件名进行URL编码,调用方无需关注;
   *    SDK会自动处理filename可能为空的情况,默认为YYYY-MM-DD-HH+扩展名;
   *    如果文件命中包含不支持URL编码的字符同样使用默认文件名;
   * @param extension 文件扩展名 不需要带点号 （注: SDK会自动处理extensions可能为空的情况,默认为 bin）
   * 注:当文件名本身扩展名与extension不一致时,以extension为准;
   * @param fileSize 文件大小 单位byte 不能为空 取值范围[1,104857600],大文件上传请使用分片上传;
   * @return 签名及上传接口信息 {@link UploadFileOmit}
   * @throws StoneCommonClientException 上传信息生成失败时抛出异常
   */
  UploadFileOmit generateUploadFileOmit(String tenantId, long expireTime, FileResourceEnum resourceType,boolean isStreamUpload,
      String filename,String extension,int fileSize) throws StoneCommonClientException;
}
