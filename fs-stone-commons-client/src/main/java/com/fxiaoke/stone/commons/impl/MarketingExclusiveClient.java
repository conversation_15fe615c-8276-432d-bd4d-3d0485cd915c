package com.fxiaoke.stone.commons.impl;

import com.fxiaoke.common.Pair;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.fxiaoke.stone.commons.domain.model.UploadFileOmit;
import com.fxiaoke.stone.commons.domain.utils.DataTimeUtil;
import com.fxiaoke.stone.commons.domain.utils.FileInfoUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.github.autoconf.ConfigFactory;
import java.util.Optional;

public class MarketingExclusiveClient extends BusinessPresetClient implements SystemPresetClient {
  String requestTemplate;
  long singleFileLimit;
  public MarketingExclusiveClient(OkHttpSupport client) {
    super(client, BusinessEnum.MARKETING);
    loadDomainEnvCache();
    registerConfiguration();
  }

  public MarketingExclusiveClient(OkHttpSupport client,int credentialsOmitCacheSize) {
    super(client, BusinessEnum.MARKETING, credentialsOmitCacheSize);
    loadDomainEnvCache();
    registerConfiguration();
  }

  @Override
  public String signature(long enterpriseId, String path) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support signature method,Please use MetadataExclusiveClient", 400);
  }

  @Override
  public Optional<String> generateSignature(long enterpriseId, String path)
      throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support signature method,Please use MetadataExclusiveClient", 400);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String signature, long expireTime, String filename,
      String extension) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient", 400);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, AuthModel authModel, String path, String signature, long expireTime,
      String filename, String extension) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient", 400);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId, String upstreamOwnerId,
      String path, String signature, long expireTime,
      String filename, String extensions,
      boolean isAvatar) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient", 400);
  }

  @Override
  public String generateCFileAccessUrl(String tenantId, String userId, String path,
      String signature, String extension, String business) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String fileTenantId, String filename, String extension,
      long expireTime, String business) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public UploadFileOmit generateUploadFileOmit(String tenantId, long expireTime, FileResourceEnum resourceType,boolean isStreamUpload,
      String filename, String extension, int fileSize) throws StoneCommonClientException {
    // 检查租户ID是否合法
    checkTenantId(tenantId);
    // 检查文件大小是否在限制范围内
    checkFileSize(fileSize);
    // 检查文件类型是否匹配资源类型
    checkResourceTypeMatchFileType(resourceType, extension);
    // 生成过期时间戳
    long expiresTimestamp = DataTimeUtil.getExpiresTimestamp(expireTime);
    // 判断企业所在云并获取域名
    EnvEIMapping env = getEnvEIMapping(tenantId);
    Pair<String, String> filenamePair = getFilename(extension, filename);
    checkFilenameLength(filenamePair.first);
    UploadFileOmit uploadFileOmit = new UploadFileOmit();
    uploadFileOmit.setMethod("POST");
    uploadFileOmit.setUrl(String.format(requestTemplate, env.getProtocol(), env.getDomain(), env.getContextPath()));
    String acid = tenantId + ".-10000";
    uploadFileOmit.setAcid(acid);
    String resource = resourceType.getResource();
    uploadFileOmit.setResource(resource);
    uploadFileOmit.setExpiry(expiresTimestamp);
    uploadFileOmit.setFilename(filenamePair.second);
    uploadFileOmit.setSize(fileSize);
    // 获取签名AS
    CredentialsOmit credentialsOmit = credentialsOmitCache.getUnchecked(Long.parseLong(tenantId));
    uploadFileOmit.setAk(credentialsOmit.getAccessKey());
    String raw = SignatureUtil.generatorRaw(acid, expiresTimestamp,resource,fileSize);
    // 生成签名
    String signature = SignatureUtil.getSignatureWithHmacSha1(credentialsOmit.getSecretKey(), raw);
    uploadFileOmit.setSign(signature);
    // 生成消息摘要
    String digest = SignatureUtil.messageDigest(SignatureUtil.generatorRaw(raw,signature,credentialsOmit.getAccessKey(),filenamePair.first));
    uploadFileOmit.setDigest(digest);
    if (isStreamUpload){
      uploadFileOmit.setContentType("application/octet-stream");
    }
    return uploadFileOmit;
  }

  private void checkTenantId(String tenantId) {
    if (tenantId == null || tenantId.isEmpty()) {
      throw new StoneCommonClientException("TenantId cannot be empty", 400);
    }

    try {
      Long.parseLong(tenantId);
    } catch (NumberFormatException e) {
      throw new StoneCommonClientException("TenantId must be a number", 400);
    }
  }

  private void checkFileSize(long fileSize) {
    if (fileSize <= 0) {
      throw new StoneCommonClientException("File size is too small,Please set the file size more than 0", 400);
    }
    if (fileSize > singleFileLimit) {
      throw new StoneCommonClientException("File size is too large,Please set the file size less than " + singleFileLimit, 400);
    }
  }

  private void checkFilenameLength(String filename) {
    if (filename.length() > 128) {
      throw new StoneCommonClientException("File name is too long,Please set the file name less than 128", 400);
    }
  }

  private void checkResourceTypeMatchFileType(FileResourceEnum resourceType, String extension) {
    if (resourceType.getResource().contains("C") && !FileInfoUtil.isSupportImageByExtension(extension)) {
      throw new StoneCommonClientException("C | TC ResourceType must be a JPG, jpeg, PNG, GIF, BMP, one of the webp type, file the actual type fake will return N | TN ", 400,resourceType.getResource(),extension);
    }
  }

  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      serverUrl = config.get("cms.stone.commons.client.getPreAskTemplate");
      requestTemplate = config.get("cms.stone.commons.client.generatePutFileTemplate");
      singleFileLimit = config.getLong("cms.stone.commons.client.singleFileLimit",104857600L);
    });
    loadDomainEnvCache();
  }

}
