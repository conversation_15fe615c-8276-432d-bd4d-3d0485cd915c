package com.fxiaoke.stone.commons.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.domain.R;
import com.fxiaoke.stone.commons.domain.api.GetPresignedCredentialsAsk.Result;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.EnvEIMapping;
import com.fxiaoke.stone.commons.domain.utils.FilenameUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.fxiaoke.stone.commons.help.SecureCryptHelp;
import com.fxiaoke.stone.commons.util.CallServiceUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigEiHelper;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.moandjiezana.toml.Toml;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;

public class BusinessPresetClient {
  String business;
  String serverUrl;
  private final OkHttpSupport client;
  private int credentialsOmitCacheSize= 5000;
  LoadingCache<Long, CredentialsOmit> credentialsOmitCache;
  Cache<String, EnvEIMapping> envEiMappingCache=CacheBuilder.newBuilder().maximumSize(200).build();
  public BusinessPresetClient(OkHttpSupport client,BusinessEnum business) {
    this.client = client;
    this.business=business.getBusiness();
    loadCache();
  }
  public BusinessPresetClient(OkHttpSupport client,BusinessEnum business,int credentialsOmitCacheSize) {
    this.client = client;
    this.business=business.getBusiness();
    this.credentialsOmitCacheSize = credentialsOmitCacheSize;
    loadCache();
  }

  private void loadCache(){
    credentialsOmitCache = CacheBuilder.newBuilder().maximumSize(credentialsOmitCacheSize)
        .build(new CacheLoader<Long, CredentialsOmit>() {
          @Nonnull
          @Override
          public CredentialsOmit load(@Nonnull Long key) {
            return getCredentials(key);
          }
        });
  }

  private CredentialsOmit getCredentials(Long enterpriseId) {
    String requestUrl = String.format(serverUrl, enterpriseId, business);
    String resultStr = CallServiceUtil.get(client, requestUrl);
    R<Result> result;
    try {
      result = JSON.parseObject(resultStr, new TypeReference<R<Result>>(Result.class) {});
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call stone-auth service,return type mismatch",500, requestUrl,resultStr);
    }
    if (!result.isSuccess()) {
      throw new StoneCommonClientException(result.getMessage(),result.getCode(),enterpriseId);
    }
    return new CredentialsOmit(result.getData().getAccessKey(), result.getData().getSecretKey());
  }


  protected Pair<String, String> getFilename(String extension, String filename) {
    // 设置默认扩展名为"bin"
    extension = Strings.isNullOrEmpty(extension) ? "bin" : extension;
    // 如果文件名为空,则生成新的文件名
    String processedFilename = Strings.isNullOrEmpty(filename) ? FilenameUtil.generateFileName(extension) : filename;
    // 如果文件名扩展名与extension不一致,则以extension为准
    if (!FilenameUtil.getExtension(processedFilename).equals(extension)) {
      processedFilename = FilenameUtil.replaceExtension(processedFilename, extension);
    }
    // 对文件名进行URL编码
    try {
      return Pair.of(processedFilename, FilenameUtil.encode(processedFilename));
    } catch (UnsupportedEncodingException e) {
      // 处理编码异常，返回一个默认的编码后文件名
      String defaultFilename = FilenameUtil.generateFileName(extension);
      return Pair.of(defaultFilename, defaultFilename);
    }
  }
  protected Pair<Boolean, String> getAcid(String tenantId, String userId, String outTenantId, String outUserId,String upstreamOwnerId, AuthModel authModel) {
    if (authModel==AuthModel.COOKIE_ALL){
      if (Strings.isNullOrEmpty(tenantId)) {
        throw new StoneCommonClientException("tenantId cannot be null or empty", 400);
      }
      return Pair.of(false,tenantId);
    }
    return getAcid(tenantId, userId, outTenantId, outUserId,upstreamOwnerId);
  }

  protected Pair<Boolean, String> getAcid(String tenantId, String userId, String outTenantId, String outUserId,String upstreamOwnerId) {
    if (Strings.isNullOrEmpty(tenantId)) {
      throw new StoneCommonClientException("tenantId cannot be null or empty", 400);
    }
    // 命中下游企业访问
    if (!Strings.isNullOrEmpty(outTenantId) &&!Strings.isNullOrEmpty(outUserId)) {
      if (Strings.isNullOrEmpty(upstreamOwnerId)){
        upstreamOwnerId="-9527";
      }
      return Pair.of(true,String.join(".", tenantId,upstreamOwnerId,outTenantId, outUserId));
    }
    if (Strings.isNullOrEmpty(userId)) {
      throw new StoneCommonClientException(
          "non-downstream enterprise access, userId cannot be null or empty", 400);
    }
    // 命中上游企业访问
    return Pair.of(false,String.join(".", tenantId, userId));
  }

  private String getClusterName(String tenantId) {
    return ConfigEiHelper.getInstance().getClusterName(tenantId);
  }

  private boolean isCurrentCloud(String tenantId) {
    return ConfigEiHelper.getInstance().isCurrentCloud(tenantId);
  }

  /**
   * 根据clusterName获取EnvEIMapping
   * @param clusterName 集群名称
   * @return {@link EnvEIMapping}
   */
  private EnvEIMapping getEnvEIMappingByCluster(String clusterName) {
    EnvEIMapping env = envEiMappingCache.getIfPresent(clusterName);
    if (env == null) {
      throw new StoneCommonClientException(
          "Get env config error on the clusterName,please check the [ domain-env-mapping-toml ] config file",
          500, clusterName);
    }
    return env;
  }

  private static final String CROSS_CLUSTER_NAME="facishare";

  protected EnvEIMapping getFacishareEnvEIMapping(){
    return getEnvEIMappingByCluster(CROSS_CLUSTER_NAME);
  }

  protected EnvEIMapping getEnvEIMapping(String tenantId) {
    String clusterName = getClusterName(tenantId);
    return getEnvEIMappingByCluster(clusterName);
  }

  private boolean isCrossFile(String path){
    return path.startsWith("A_")||path.startsWith("TA_")||path.startsWith("G_");
  }

  /**
   * 根据tenantId和path获取EnvEIMapping
   * @param tenantId 租户ID
   * @param path 文件Id
   * @return {@link EnvEIMapping}
   */
  protected EnvEIMapping getEnvEIMapping(String tenantId,String path) {
    String clusterName =getClusterName(tenantId);
    // A|TA|G文件属于Cross文件系统,该文件系统仅在foneshare集群部署,因此需要特殊处理
    if (isCrossFile(path)){
      return getFacishareEnvEIMapping();
    }
    EnvEIMapping env = envEiMappingCache.getIfPresent(clusterName);
    if (env == null) {
      throw new StoneCommonClientException(
          "Get env config error on the tenantId,please check the [ domain-env-mapping-toml ] config file",
          500, tenantId, clusterName);
    }
    return env;
  }

  protected CredentialsOmit getCredentialsOmit(String tenantId, String fileEi,String path) {
    // A|TA|G文件直接返回foneshare环境访问ASK
    if (isCrossFile(path)||!isCurrentCloud(fileEi)) {
      String clusterName = getClusterName(tenantId);
      EnvEIMapping env = getEnvEIMappingByCluster(clusterName);
      return new CredentialsOmit(env.getAccessKey(), env.getSecretKey());
    }
    return credentialsOmitCache.getUnchecked(Long.parseLong(tenantId));
  }

  protected void loadDomainEnvCache(){
    ConfigFactory.getInstance().getConfig("domain-env-mapping-toml", config -> {
      Toml toml = new Toml().read(config.getString());
      for (Map.Entry<String, Object> entry : toml.entrySet()) {
        Toml entryValue = (Toml) entry.getValue();
        EnvEIMapping envEIMapping = new EnvEIMapping();
        envEIMapping.setCdnDomain(entryValue.getString("cdnDomain"));
        envEIMapping.setHomeDomain(entryValue.getString("homeDomain"));
        envEIMapping.setDomain(entryValue.getString("domain"));
        envEIMapping.setProtocol(entryValue.getString("protocol"));
        envEIMapping.setContextPath(entryValue.getString("contextPath"));
        envEIMapping.setCdnContextPath(entryValue.getString("cdnContextPath"));
        String encryAk = entryValue.getString("accessKey");
        String encrySk = entryValue.getString("secretKey");
        if (!Strings.isNullOrEmpty(encryAk) && !Strings.isNullOrEmpty(encrySk)) {
          String accessKey = PasswordUtil.decode(encryAk);
          String secretKey = PasswordUtil.decode(encrySk);
          if (Strings.isNullOrEmpty(accessKey) || Strings.isNullOrEmpty(secretKey)) {
            throw new StoneCommonClientException(
                "accessKey or secretKey is empty,please check the [ domain-env-mapping-toml ] config file",
                500);
          }
          envEIMapping.setAccessKey(accessKey);
          envEIMapping.setSecretKey(secretKey);
        }
        if (envEIMapping.isConfigItemEmpty()){
          throw new StoneCommonClientException(
              "[ domain-env-mapping-toml ] config file ,env [ "+entry.getKey()+" ] config item exists empty config ,please check the ",
              500);
        }
        envEiMappingCache.put(entry.getKey(), envEIMapping);
      }
    });
  }

  protected String getFid(long tenantId, String path) {
    return SecureCryptHelp.encry(SignatureUtil.generatorRaw(tenantId, path));
  }

  /**
   * 签名加密方法(注:不建议缓存加密签名,签名每次加密后都不同);
   * @param said 言(一般是tenantId);
   * @param signature 文件元数据签名;
   * @return 加密后的字符串，格式为Enc(加密内容)
   * 注：已加密的签名不会再次加密将原样返回,非法参数返回Optional.empty()
   * 解密请使用以下方法:{@link #decryptSignature(String said,String encCiphertext)}
   */
  public Optional<String> encryptSignature(String said,String signature) {
    if (said==null || said.isEmpty() || signature==null || signature.isEmpty()) {
      return Optional.empty();
    }
    if (SecureCryptHelp.isEncry(signature)) {
      return Optional.of(signature);
    }
    String raw = SecureCryptHelp.generatorRaw(said, signature);
    return Optional.of(SecureCryptHelp.signEncry(raw));
  }

  /**
   * 加密签名解密方法
   * @param said 用于验证的言(一般是tenantId);
   * @param encCiphertext 加密的签名;
   * @return 返回解密后的原始signature
   * 注：非加密的签名会被原样返回,非法参数返回Optional.empty()
   * 加密请使用以下方法:{@link #encryptSignature(String said,String signature)}
   */
  public Optional<String> decryptSignature(String said, String encCiphertext) {
    if (said==null || said.isEmpty() || encCiphertext==null || encCiphertext.isEmpty()) {
      return Optional.empty();
    }
    // 检查是否符合Enc()加密格式,如果不符合则原样返回
    if (!SecureCryptHelp.isEncry(encCiphertext)){
      return Optional.of(encCiphertext);
    }
    // 提取实际的密文
    String ciphertext = SecureCryptHelp.extractCiphertext(encCiphertext);
    // 解密
    String raw = SecureCryptHelp.signDecry(ciphertext);
    List<String> saidAndSignature = Splitter.on(Constants.DELIMITER).omitEmptyStrings().limit(2)
        .splitToList(raw);
    // 符合Enc()加密格式但格式错误且said不匹配则返回空,视为伪造
    if (saidAndSignature.size()!=2||!saidAndSignature.get(0).equals(said)) {
      return Optional.empty();
    }
    return Optional.of(saidAndSignature.get(1));
  }
}
