package com.fxiaoke.stone.commons.util;

public class URLSplice {

  // %s://%s/%sSign?Fid=%s&Acid=%s&Ets=%d&Ak=%s&Fn=%s&Sig=%s&Ds=%s
  public static String generateSignUrl(String protocol,String domain,String contextPath,String fid,String acid,long ets,String ak,String fn,String sig,String ds){
   return protocol+"://"+domain+"/"+contextPath+"Sign?Fid="+fid+"&Acid="+acid+"&Ets="+ets+"&Ak="+ak+"&Fn="+fn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%sSign/AuthXC?Fid=%s&Acid=%s&Ets=%d&Ak=%s&Fn=%s&Sig=%s&Ds=%s
  public static String generateSignFsAuthXCUrl(String protocol,String domain,String contextPath,String fid,String acid,long ets,String ak,String fn,String sig,String ds){
    return protocol+"://"+domain+"/"+contextPath+"Sign/AuthXC?Fid="+fid+"&Acid="+acid+"&Ets="+ets+"&Ak="+ak+"&Fn="+fn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%sSign/Em6?Fid=%s&Acid=%s&Ets=%d&Ak=%s&Fn=%s&Sig=%s&Ds=%s
  public static String generateSignEm6Url(String protocol,String domain,String contextPath,String fid,String acid,long ets,String ak,String fn,String sig,String ds){
    return protocol+"://"+domain+"/"+contextPath+"Sign/Em6?Fid="+fid+"&Acid="+acid+"&Ets="+ets+"&Ak="+ak+"&Fn="+fn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%sAnonymity/Sign?Fid=%s&Acid=%s&Ets=%d&Ak=%s&Fn=%s&Bn=%s&Sig=%s&Ds=%s
  public static String generateAnonymitySign(String protocol,String domain,String contextPath,String fid,String acid,long ets,String ak,String fn,String bn,String sig,String ds){
    return protocol+"://"+domain+"/"+contextPath+"Anonymity/Sign?Fid="+fid+"&Acid="+acid+"&Ets="+ets+"&Ak="+ak+"&Fn="+fn+"&Bn="+bn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%sCookie?Fid=%s&Acid=%s&Ak=%s&Fn=%s&Sig=%s&Ds=%s
  public static String generateCookieUrl(String protocol,String domain,String contextPath,String fid,String acid,String ak,String fn,String sig,String ds){
    return protocol+"://"+domain+"/"+contextPath+"Cookie?Fid="+fid+"&Acid="+acid+"&Ak="+ak+"&Fn="+fn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%sCookie/AuthXC?Fid=%s&Acid=%s&Ak=%s&Fn=%s&Sig=%s&Ds=%s
  public static String generateCookieFsAuthXCUrl(String protocol,String domain,String contextPath,String fid,String acid,String ak,String fn,String sig,String ds){
    return protocol+"://"+domain+"/"+contextPath+"Cookie/AuthXC?Fid="+fid+"&Acid="+acid+"&Ak="+ak+"&Fn="+fn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%sCookie/Em6?Fid=%s&Acid=%s&Ak=%s&Fn=%s&Sig=%s&Ds=%s
  public static String generateCookieEm6Url(String protocol,String domain,String contextPath,String fid,String acid,String ak,String fn,String sig,String ds){
    return protocol+"://"+domain+"/"+contextPath+"Cookie/Em6?Fid="+fid+"&Acid="+acid+"&Ak="+ak+"&Fn="+fn+"&Sig="+sig+"&Ds="+ds;
  }

  // %s://%s/%s?Cid=%s&Acid=%s&Ext=%s&Bn=%s&Ds=%s
  public static String generateCFileAccessUrl(String protocol,String domain,String contextPath,String cid,String acid,String ext,String bn,String ds){
    return protocol+"://"+domain+"/"+contextPath+"?Cid="+cid+"&Acid="+acid+"&Ext="+ext+"&Bn="+bn+"&Ds="+ds;
  }

  // // %s://%s/%s?Fid=%s&Acid=%s&Fn=%s&Ds=%s
  public static String generateAvatarUrl(String protocol,String domain,String contextPath,String fid,String acid,String fn,String ds){
    return protocol+"://"+domain+"/"+contextPath+"?Fid="+fid+"&Acid="+acid+"&Fn="+fn+"&Ds="+ds;
  }

}
