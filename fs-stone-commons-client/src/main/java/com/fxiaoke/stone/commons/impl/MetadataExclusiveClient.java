package com.fxiaoke.stone.commons.impl;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.fxiaoke.stone.commons.domain.constant.BusinessEnum;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.domain.model.CredentialsOmit;
import com.fxiaoke.stone.commons.domain.model.UploadFileOmit;
import com.fxiaoke.stone.commons.domain.utils.FilenameUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.github.autoconf.ConfigFactory;
import java.util.Optional;

public class MetadataExclusiveClient extends BusinessPresetClient implements SystemPresetClient {

  public MetadataExclusiveClient(OkHttpSupport client) {
    super(client, BusinessEnum.METADATA);
    registerConfiguration();
  }

  public MetadataExclusiveClient(OkHttpSupport client, int credentialsOmitCacheSize) {
    super(client, BusinessEnum.METADATA, credentialsOmitCacheSize);
    registerConfiguration();
  }

  public String signature(long enterpriseId, String path){
    // 获取签名ASK
    CredentialsOmit credentialsOmit = credentialsOmitCache.getUnchecked(enterpriseId);
    // 拼接签名密文,并对path进行处理,防止出现path包含扩展名(这里只考虑N_yyyy_mm_uuid.jpg的情况)
    String raw = SignatureUtil.generatorRaw(enterpriseId, FilenameUtil.getFirstSeparatorBeforeName(path));
    // 生成签名
    return SignatureUtil.generatorRaw(enterpriseId,SignatureUtil.getSignatureWithHmacSha1(credentialsOmit.getSecretKey(), raw),credentialsOmit.getAccessKey());
  }

  @Override
  public Optional<String> generateSignature(long enterpriseId, String path) throws StoneCommonClientException {
    if (path == null || path.isEmpty() || path.startsWith("http")) {
      return Optional.empty();
    }
    // 获取签名ASK
    CredentialsOmit credentialsOmit = credentialsOmitCache.getUnchecked(enterpriseId);
    // 拼接签名密文,并对path进行处理,防止出现path包含扩展名(这里只考虑N_yyyy_mm_uuid.jpg的情况)
    String raw = SignatureUtil.generatorRaw(enterpriseId, FilenameUtil.getFirstSeparatorBeforeName(path));
    String signature = SignatureUtil.generatorRaw(enterpriseId,
        SignatureUtil.getSignatureWithHmacSha1(credentialsOmit.getSecretKey(), raw),
        credentialsOmit.getAccessKey());
    // 生成签名
    return Optional.of(signature);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String signature, long expireTime, String filename,
      String extension) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, AuthModel authModel, String path, String signature, long expireTime,
      String filename, String extension) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public String generateUrl(String tenantId,String userId,String outTenantId,String outUserId,String upstreamOwnerId,
      String path,String signature,long expireTime,
      String filename,String extensions,
      boolean isAvatar) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public String generateCFileAccessUrl(String tenantId, String userId, String path,
      String signature, String extension, String business) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public String generateUrl(String tenantId, String userId, String outTenantId, String outUserId,
      String upstreamOwnerId, String path, String fileTenantId, String filename, String extension,
      long expireTime, String business) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generateUrl method,Please use AppFrameworkExclusiveClient",400);
  }

  @Override
  public UploadFileOmit generateUploadFileOmit(String tenantId, long expireTime, FileResourceEnum resourceType,boolean isStreamUpload,
      String filename, String extension, int fileSize) throws StoneCommonClientException {
    throw new StoneCommonClientException("Not support generatePutUrl method,Please use MarketingExclusiveClient", 400);
  }

  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      serverUrl = config.get("cms.stone.commons.client.getPreAskTemplate");
    });
  }
}
