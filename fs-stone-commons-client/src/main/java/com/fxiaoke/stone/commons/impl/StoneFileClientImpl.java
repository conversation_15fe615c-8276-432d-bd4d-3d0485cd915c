package com.fxiaoke.stone.commons.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneFileClient;
import com.fxiaoke.stone.commons.domain.api.PathToCdnFile;
import com.fxiaoke.stone.commons.domain.api.PathToCdnFile.Result;
import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.api.adapt.RestEasyR;
import com.fxiaoke.stone.commons.domain.api.adapt.StoneFileUploadApi;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.util.CallServiceUtil;
import com.fxiaoke.stone.commons.domain.utils.CodingUtils;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

/**
 * Stone文件客户端实现类.
 * <p>
 * 负责处理文件的上传逻辑，包括有已知文件大小的流式上传和未知文件大小的缓存上传。
 * 通过OkHttp与后端服务进行通信，并利用autoconf进行配置管理。
 * </p>
 */
@Slf4j
public class StoneFileClientImpl implements StoneFileClient {

  private final static String MODULE = "StoneFileClientImpl";

  private final OkHttpSupport client;

  private String stoneUploadFileRequestTemplate;
  private String stoneUploadTempFileRequestTemplate;

  // API 定义常量
  private static final String USER_EXT_HEADER_NAME = "x-fs-rest-user-ext";
  private static final MediaType OCTET_STREAM_MEDIA_TYPE = MediaType.parse(
      "application/octet-stream");

  /**
   * 构造函数.
   *
   * @param client OkHttp客户端实例，由外部注入
   */
  public StoneFileClientImpl(OkHttpSupport client) {
    this.client = client;
    // 初始化时注册并加载配置
    registerConfiguration();
  }

  /**
   * 上传文件.
   * <p>
   * 这是文件上传的核心入口方法。它会根据输入流是否能提供确切的文件大小，
   * 自动选择流式上传（性能更优）或缓存上传（适用于未知大小的流）。
   * </p>
   *
   * @param arg         上传参数，包含元数据如企业账号、员工ID、业务类型等
   * @param cacheConfig 缓存配置，当文件大小未知时使用
   * @param stream      文件输入流
   * @return 上传结果，包含文件路径、大小等信息
   * @throws StoneCommonClientException 自定义业务异常，例如参数校验失败、文件过大等
   * @throws IOException                当发生网络或文件IO错误时抛出
   */
  @Override
  public StoneUploadFile.Result uploadFile(StoneUploadFile.Arg arg,
      StoneUploadFile.CacheConfig cacheConfig, InputStream stream)
      throws StoneCommonClientException, IOException {

    log.info("{}.{} Start, arg:{}", MODULE, "uploadFile", arg);

    // 步骤1: 校验输入参数的合法性
    CodingUtils.throwIfInvalid(arg, stream);

    // 步骤2: 根据文件大小是否已知，选择不同的上传策略
    StoneUploadFile.Result result;
    if (arg.getFileSize() == null || arg.getFileSize() <= 0) {
      // 文件大小未知：先缓存到本地临时文件，再上传
      result = uploadWithNoLengthStream(arg, cacheConfig, stream);
    } else {
      result = uploadWithHasLengthStream(arg,stream);
    }
    log.info("{}.{} End, result:{}", MODULE, "uploadFile", result);

    return result;
  }

  @Override
  public Result pathToCdnFile(PathToCdnFile.Arg arg) {
    return null;
  }

  /**
   * 使用缓存方式上传文件（适用于未知长度的流）.
   * <p>
   * 该方法首先将输入流写入本地临时文件，在此过程中计算文件的实际大小并进行大小校验。
   * 然后将临时文件作为请求体上传到服务器。
   * 采用try-finally结构确保临时文件在操作结束后总能被删除。
   * </p>
   *
   * @param arg         上传参数
   * @param cacheConfig 缓存配置
   * @param stream      文件输入流
   * @return 上传结果
   * @throws StoneCommonClientException 如果文件大小超过缓存限制，则抛出此异常
   * @throws IOException                在读写临时文件或网络请求时可能抛出
   */
  private StoneUploadFile.Result uploadWithNoLengthStream(StoneUploadFile.Arg arg,
      StoneUploadFile.CacheConfig cacheConfig, InputStream stream)
      throws StoneCommonClientException, IOException {
    Path chchePath = cacheConfig.getCachePath();
    try {

      // 阶段1: 将输入流数据缓存到本地文件
      int actualSize = streamToFile(stream, chchePath, cacheConfig.getBufferSize(),
          cacheConfig.getMaxCacheSize(), arg);
      // 更新参数中的文件大小
      arg.setFileSize(actualSize);

      // 阶段2: 创建基于文件的请求体并执行上传
      RequestBody requestBody = RequestBody.create(chchePath.toFile(), OCTET_STREAM_MEDIA_TYPE);
      return executeUpload(arg, requestBody);
    } finally {
      // 资源清理阶段 - 独立处理，不影响主流程（允许向上抛出异常)
      Files.deleteIfExists(chchePath);
    }
  }

  /**
   * 使用流式上传方式上传文件（适用于已知长度的流）.
   * <p>
   * 该方法直接将输入流作为请求体上传到服务器，避免了临时文件的创建和删除，
   * 提高了上传效率。适用于文件大小已知的场景。
   * </p>
   *
   * @param arg    上传参数
   * @param stream 文件输入流
   * @return 上传结果
   * @throws StoneCommonClientException 在HTTP调用或JSON解析失败时抛出
   */
  private StoneUploadFile.Result uploadWithHasLengthStream(StoneUploadFile.Arg arg,InputStream stream){
    // 文件大小已知：直接使用流式上传，避免本地缓存，效率更高
    RequestBody requestBody = createStreamingRequestBody(stream, arg.getFileSize().longValue());
    return executeUpload(arg, requestBody);
  }

  /**
   * 将输入流写入到指定的缓存文件路径.
   * <p>
   * 边读边写，并在过程中实时检查文件大小是否超出预设的最大缓存限制。
   * </p>
   *
   * @param stream       源输入流
   * @param cachePath    目标缓存文件路径
   * @param bufferSize   读取时使用的缓冲区大小
   * @param maxCacheSize 允许的最大缓存文件大小
   * @param arg          上传参数，用于异常信息构造
   * @return 文件的实际大小（字节）
   * @throws StoneCommonClientException 如果文件大小超过maxCacheSize，抛出此异常
   * @throws IOException                当发生文件读写错误时抛出
   */
  private int streamToFile(InputStream stream, Path cachePath, int bufferSize, int maxCacheSize,
      StoneUploadFile.Arg arg) throws StoneCommonClientException, IOException {
    int actualSize = 0;
    byte[] buffer = new byte[bufferSize];
    int bytesRead;

    try (OutputStream os = Files.newOutputStream(cachePath)) {
      while ((bytesRead = stream.read(buffer)) != -1) {

        // 累加已读取的字节数
        actualSize += bytesRead;

        // 业务限制检查：实时校验文件大小是否超限
        if (actualSize > maxCacheSize) {
          String message = String.format("File size %d bytes exceeds max cache limit of %d bytes",
              actualSize, maxCacheSize);
          throw new StoneCommonClientException(message, 413, arg);
        }

        // 写入临时文件 - 区分输出流异常
        os.write(buffer, 0, bytesRead);
      }

      log.debug("Cached file size: {} bytes", actualSize);
      return actualSize;
    }
  }

  /**
   * 执行实际的HTTP上传请求.
   * <p>
   * 此方法是上传流程的最后一步，它负责：
   * 1. 将客户端参数适配为API所需的请求格式。
   * 2. 设置HTTP请求头。
   * 3. 调用HTTP客户端发送POST请求。
   * 4. 解析服务器返回的JSON响应。
   * 5. 将API响应适配为客户端所需的结果格式。
   * </p>
   *
   * @param arg         上传参数
   * @param requestBody 已经构建好的请求体（可以是流式或基于文件的）
   * @return 上传结果
   * @throws StoneCommonClientException 在HTTP调用、JSON解析或服务器返回错误时抛出
   */
  private StoneUploadFile.Result executeUpload(StoneUploadFile.Arg arg, RequestBody requestBody)
      throws StoneCommonClientException {

    // 阶段1: 数据转换 - 将客户端参数转换为API请求对象
    StoneFileUploadApi.Req apiRequest = adaptToApiRequest(arg);
    // 将API请求对象序列化为JSON字符串，并放入自定义请求头中
    Map<String, String> headers = Collections.singletonMap(USER_EXT_HEADER_NAME,
        JSON.toJSONString(apiRequest));

    // 阶段2: HTTP调用 - 发送POST请求
    String serverUrl = getServerUrl(arg.getResourceType());
    String responseBody = CallServiceUtil.post(client, serverUrl, headers, requestBody);

    // 响应解析阶段 - 可能抛出数据格式异常
    RestEasyR<StoneFileUploadApi.Res> apiResponse = CallServiceUtil.formJson(responseBody,stoneUploadFileRequestTemplate,
        StoneFileUploadApi.Res.class);

    return adaptToClientResult(apiResponse);
  }

  /**
   * 将客户端上传参数 {@link StoneUploadFile.Arg} 适配为后端API所需的请求对象 {@link StoneFileUploadApi.Req}.
   *
   * @param arg 客户端上传参数
   * @return API请求对象
   */
  private StoneFileUploadApi.Req adaptToApiRequest(StoneUploadFile.Arg arg) {
    StoneFileUploadApi.Req req = new StoneFileUploadApi.Req();
    req.setEa(arg.getEa());
    req.setEmployee_id(arg.getEmployeeId());
    req.setBusiness(arg.getBusiness());
    req.setSecurity_group(arg.getSecurityGroup());
    req.setExtension_name(arg.getExtension());
    req.setExpire_day(arg.getExpireDay());
    req.setFile_size(arg.getFileSize());
    req.setCode(arg.getHashCode());
    req.setKeep_format(arg.getKeepFormat());
    FileResourceEnum resourceType = arg.getResourceType();
    if (resourceType==FileResourceEnum.C||resourceType==FileResourceEnum.TC) {
      req.setNeed_cdn(true);
    }
    return req;
  }

  /**
   * 将后端API的响应对象 {@link StoneFileUploadApi.Res} 适配为客户端所需的结果对象 {@link StoneUploadFile.Result}.
   *
   * @param restEasyR API响应对象
   * @return 客户端结果对象
   */
  private StoneUploadFile.Result adaptToClientResult(RestEasyR<StoneFileUploadApi.Res> restEasyR) {
    StoneUploadFile.Result result = new StoneUploadFile.Result();
    StoneFileUploadApi.Res res= restEasyR.getData();
    result.setPath(res.getPath());
    result.setSize(res.getSize());
    result.setExtension(res.getExtensionName());
    return result;
  }

  /**
   * 创建一个基于OkHttp的流式请求体（RequestBody）.
   * <p>
   * 这种方式可以直接将InputStream中的数据发送出去，而无需将其完全加载到内存中，
   * 对于大文件上传非常高效。它利用了Okio库来高效处理IO操作。
   * </p>
   *
   * @param inputStream   文件输入流
   * @param contentLength 文件的确切大小（长度）
   * @return 一个配置好的RequestBody实例
   */
  private RequestBody createStreamingRequestBody(final InputStream inputStream,
      final long contentLength) {
    return new RequestBody() {

      /**
       * 定义请求体的内容类型.
       * @return 二进制流媒体类型
       */
      @Override
      public MediaType contentType() {
        return OCTET_STREAM_MEDIA_TYPE;
      }

      /**
       * 定义请求体的长度.
       * <p>
       * 返回确切的字节数对于服务端处理（如进度条）和网络传输优化（如Content-Length头）至关重要。
       * </p>
       * @return 文件内容的长度
       */
      @Override
      public long contentLength() {
        return contentLength;
      }

      /**
       * 将输入流的内容写入OkHttp的Sink中.
       * <p>
       * OkHttp会调用此方法来获取要发送的数据。
       * 使用try-with-resources和Okio可以确保资源被正确管理，并实现高效的IO传输。
       * </p>
       * @param sink OkHttp提供的数据写入目标
       * @throws IOException 如果从输入流读取或向Sink写入时发生错误
       */
      @Override
      public void writeTo(@NotNull BufferedSink sink) throws IOException {
        try (Source source = Okio.source(inputStream)) {
          sink.writeAll(source);
        }
      }
    };
  }

  /**
   * 注册并加载配置.
   * <p>
   * 从配置工厂获取"fs-stone-common"配置，并监听其变化。
   * 主要用于获取文件上传的URL模板。
   * </p>
   */
  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      stoneUploadFileRequestTemplate = config.get(
          "cms.stone.commons.client.stoneUploadFileRequestTemplate");
      stoneUploadTempFileRequestTemplate = config.get(
          "cms.stone.commons.client.stoneUploadTempFileRequestTemplate");
    });
  }

  private String getServerUrl(FileResourceEnum resourceType) {
    if (resourceType == FileResourceEnum.TN || resourceType == FileResourceEnum.TC) {
      return stoneUploadTempFileRequestTemplate;
    } else if (resourceType == FileResourceEnum.N || resourceType == FileResourceEnum.C) {
      return stoneUploadFileRequestTemplate;
    }
    throw new StoneCommonClientException(
        resourceType + " is not supported", 400);
  }

}
